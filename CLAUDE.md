# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Development
- `npm run dev` or `npm start` - Start development server at http://localhost:3000
- `npm run build` - Build for production (includes prep, build:dev, and compression)
- `npm run build:dev` - Build development version with optimization
- `npm run clean` - Clean build artifacts from js/assets/bundle and fonts/images
- `npm run prep` - Install dependencies, clean, and generate version file

### Docker Development
- `docker build -f Dockerfile.cloudrun -t atlas-mayo .` - Build Docker image
- `docker run -p 8080:8080 atlas-mayo` - Run Docker container (accessible at http://localhost:8080/atlas/)

### Deployment
- `./deploy-to-cloudrun.sh` - Deploy to Google Cloud Run

## Architecture Overview

This is a **Mayo Clinic branded version of OHDSI Atlas**, a web application for analyzing observational health data using the OMOP Common Data Model. The application is built with a **modular AMD/RequireJS architecture** using Knockout.js for data binding.

### Key Technologies
- **Frontend Framework**: Knockout.js 3.5.1 with Bootstrap 3.4.1
- **Module System**: AMD with RequireJS
- **Bundling**: Custom build system using Node.js optimization
- **Styling**: LESS preprocessing with Mayo Clinic branding overlays
- **Data Visualization**: D3.js 4.13.0 and custom charting libraries
- **Backend Integration**: RESTful API communication via services layer

### Application Structure

#### Core Application (`js/`)
- **Application.js**: Main application class with router and shared state management
- **main.js**: Entry point with RequireJS configuration and bootstrapping
- **settings.js**: RequireJS paths and CSS loading configuration
- **config/**: Configuration files including app settings and terms/conditions

#### Component Architecture (`js/components/`)
- **Modular Components**: Each component has `.js`, `.html`, and `.less` files
- **Cohort Builder**: Complex cohort definition system (`cohortbuilder/`)
- **Charts**: Data visualization components (`charts/`)
- **Concept Sets**: Medical concept management (`conceptset/`)
- **Security**: Authentication and authorization components (`security/`)

#### Page System (`js/pages/`)
- **Route-based Pages**: Each major feature area has its own page module
- **Page.js/Route.js**: Base classes for page routing
- **Router.js**: Main application router

#### Services Layer (`js/services/`)
- **API Services**: HTTP communication with backend APIs
- **Business Logic**: Analysis services (Cohort, Prediction, Estimation)
- **Utilities**: Common services (Authentication, Permissions, Jobs)

#### Mayo Clinic Branding
- **Brand CSS**: `js/styles/mayo-clinic-*.css` files override Bootstrap styles
- **Color System**: Mayo Blue (#0057B8) primary brand color
- **Typography**: Mayo Clinic Sans font family
- **Accessibility**: WCAG AA compliant color contrasts and focus states

### Key Architectural Patterns

1. **AMD Module System**: All files use `define([dependencies], function)` pattern
2. **Knockout MVVM**: Components use Knockout observables for data binding
3. **Service Layer**: Business logic separated into dedicated service modules
4. **Component Registration**: Components register with RequireJS and include HTML templates
5. **Shared State**: Global application state managed through `atlas-state.js`

### Data Flow
1. **Router** determines current page based on URL
2. **Page modules** load required components and services
3. **Services** communicate with backend APIs
4. **Components** bind to observables and render UI
5. **Charts** render data visualizations using D3.js

### Configuration
- **config.js**: Merges app configuration with local overrides
- **config-local.js**: Local environment configuration (optional)
- **settings.js**: RequireJS module and CSS path configuration

### Build Process
1. **Optimization**: `build/optimize.js` bundles and minifies JavaScript
2. **Compression**: Terser compresses the final bundle
3. **Versioning**: Automatic version generation for cache busting
4. **Font/Image Copying**: Static assets moved to js/assets/

## Development Notes

### Prerequisites
- Node.js 18+
- npm package manager

### Common Development Tasks
- **Adding new components**: Create in `js/components/` with `.js`, `.html`, `.less` files
- **Adding new pages**: Create in `js/pages/` following existing patterns
- **Modifying styles**: Edit existing `.less` files or Mayo Clinic brand CSS
- **API integration**: Add new services in `js/services/` following existing patterns

### File Watching
The development server (`dev-server.js`) serves static files and handles client-side routing. For file watching during development, use `npm run dev` which starts the Express server.

### Testing
- **Mayo Clinic Branding**: Use `mayo-clinic-branding-test.html` to test branded components
- **Local Development**: Test at http://localhost:3000 before deployment
- **Docker Testing**: Use Docker commands above to test containerized version

### Important Dependencies
- **WebAPI**: Backend API dependency (separate repository)
- **OMOP CDM**: Requires access to OMOP Common Data Model database
- **Authentication**: Configurable authentication providers via config

### Brand Compliance
- Maintain Mayo Clinic brand standards in any UI changes
- Follow WCAG AA accessibility guidelines
- Use established color palette and typography system
- Test responsive design on mobile devices

### SVG Icon Path Issues (Docker Container)
- **Problem**: SVG icons use relative paths (`../images/svg/`) which don't work in Docker containers
- **Solution**: Dynamic path resolution in `js/extensions/bindings/svgBinding.js` and `js/utils/CommonUtils.js`
- **Detection**: Checks if `window.location.pathname` starts with `/atlas/` (Docker) vs local development
- **Docker path**: Uses absolute path `/atlas/images/svg/` 
- **Local path**: Uses relative path `../images/svg/`
- **Usage**: Use `data-bind="svgHref: 'iconName'"` for new SVG elements
- **Global function**: `window.getSvgPath(iconName)` available in templates