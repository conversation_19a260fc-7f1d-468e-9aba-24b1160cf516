<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="10 -200 400 400" width="400" height="400" xml:space="preserve">
	<g id="rings">
		<circle cx="205" cy="17" r="165" stroke="#21425a" fill="none" stroke-width="15px"></circle>

		<ellipse cx="205" cy="17" rx="70" ry="70" stroke="#f28b1b" fill="none" stroke-width="6px" stroke-opacity="0.8">
			<animate attributeName="rx" begin="0s" dur="4s" values="70;1;70" calcMode="linear" repeatCount="indefinite"/>
			<animate attributeName="rx" begin="0s" dur="4s" values="70;1;70" calcMode="linear" repeatCount="indefinite"/>
			<animateTransform attributeName="transform" type="rotate" from="0 205 17" to="180 205 17" dur="2s" repeatCount="indefinite"/>
		</ellipse>

		<ellipse cx="205" cy="17" rx="100" ry="100" stroke="#21425a" fill="none" stroke-width="10px" stroke-opacity="0.8">
			<animate attributeName="rx" begin="0s" dur="6s" values="100;1;100" calcMode="linear" repeatCount="indefinite"/>
			<animate attributeName="rx" begin="0s" dur="6s" values="100;1;100" calcMode="linear" repeatCount="indefinite"/>
			<animateTransform attributeName="transform" type="rotate" from="0 205 17" to="180 205 17" dur="3s" repeatCount="indefinite"/>
		</ellipse>

		<ellipse cx="205" cy="17" rx="135" ry="135" stroke="#ccc" fill="none" stroke-width="12px" stroke-opacity="0.8">
			<animate attributeName="rx" begin="0s" dur="4s" values="130;1;130" calcMode="linear" repeatCount="indefinite"/>
			<animate attributeName="rx" begin="0s" dur="4s" values="130;1;130" calcMode="linear" repeatCount="indefinite"/>
			<animateTransform attributeName="transform" type="rotate" from="0 205 17" to="180 205 17" dur="8s" repeatCount="indefinite"/>
		</ellipse>
	</g>
	<g id="atlas">
		<g>
			<path
				fill="#20425A"
				d="M349.258,186.654l-16.739,17.205l-25.804,10.693l-101.598,8.369l-104.383-29.061l-29.758-21.155
			l-19.063-17.902l-1.163-4.417l16.273-4.649l19.994,16.738l24.643,12.322l34.175,12.319l27.433,6.509l42.778,5.813l28.131,2.093
			l27.198-0.463l33.013-6.51l16.275-9.069l10.926-8.833L349.258,186.654z"/>
			<g>
				<g>
					<polygon
						fill="#DDDDDD"
						points="175.953,252.815 168.594,238.511 166.927,231.85 136.688,220.063 129.35,220.539
					114.551,212.532 90.531,195.376 83.385,195.137 72.463,184.474 72.463,174.229 79.979,168.904 87.537,173.677 85.225,176.238
					94.013,177.444 98.934,182.479 120.846,186.589 138.693,194.238 155.03,196.31 162.29,198.069 168.135,196.337 172.06,195.55
					181.168,196.953 185.278,196.305 196.388,194.969 196.64,194.758 197.071,182.056 196.111,170.781 200.105,160.657
					211.471,156.694 221.517,158.544 228.048,166.12 231.75,177.229 232.436,195.993 233.949,198.18 238.814,201.099
					240.297,200.935 250.104,200.443 254.467,202.24 260.612,204.435 269.231,204.887 285.229,206.943 298.272,203.967
					315.284,202.626 316.214,201.579 315.119,201.434 320.729,194.094 328.868,192.889 334.499,199.115 334.786,210.546
					324.421,214.692 307.118,227.085 291.724,231.237 282.224,229.849 258.373,237.096 249.991,234.7 245.071,252.811
					206.938,259.282 				"/>
					<path
						fill="#222222"
						d="M332.752,209.206l-9.299,3.72l-17.205,12.322l-14.646,3.95l-9.532-1.394l-23.715,7.206l-9.763-2.79
					l-5.116,18.832l-36.5,6.194l-29.682-6.194l-6.817-13.252l-1.861-7.439l-31.616-12.322l-7.206,0.467l-14.183-7.674
					l-24.411-17.435l-6.975-0.233l-9.764-9.532v-8.369l5.58-3.953l4.418,2.79l-3.255,3.605l11.856,1.627l4.883,4.996l22.316,4.187
					l17.902,7.672l16.505,2.093l7.674,1.859l6.275-1.859l3.489-0.7l9.066,1.396l4.418-0.696l11.624-1.396l1.395-1.163l0.465-13.716
					l-0.93-10.926l3.485-8.835l10-3.487l8.833,1.627l5.813,6.743l3.486,10.462l0.696,19.062l2.093,3.023l5.813,3.487l2.094-0.23
					l9.299-0.467l3.949,1.627l6.51,2.326l8.836,0.464l16.275,2.093l13.249-3.023l17.669-1.393l1.859-2.094l7.672-1.626l-6.973-0.93
					l3.021-3.953l6.278-0.93l4.416,4.883L332.752,209.206z"/>
				</g>
				<path
					opacity="0.4"
					fill="#DDDDDD"
					d="M319.5,204.557l2.557,4.183l-6.043-2.09l-9.533,1.394l-15.574,8.836l-10.23,0.463
				l3.721-5.113l14.179-6.279l17.669-1.393l1.859-2.094l7.672-1.626l-6.973-0.93l6.276-2.093l3.953,4.187L319.5,204.557z
				 M154.668,198.28l-12.088,0.927l21.622,5.813l-1.86-4.88L154.668,198.28z M172.106,197.581l-3.489,0.7l-1.625,7.669l4.65,0.931
				l4.184-5.81L172.106,197.581z M260.215,206.416l-3.253,6.742l20.925-3.022l-8.836-3.256L260.215,206.416z M249.756,202.463
				l-4.186,3.257l8.602,5.81l-0.467-7.439L249.756,202.463z M97.944,184.328l-4.883-4.996l-11.856-1.627l3.255-3.605h-3.72
				l-2.79,4.882l13.019,2.557l3.255,3.023l-4.884,2.79l8.139,0.233l26.502,8.835l-2.093,3.719l6.511-2.093l6.508,4.65l0.931-3.721
				l-15.578-10.462L97.944,184.328z M216.976,207.81l-7.906,0.7l-8.369-1.86l-8.136,2.323l-6.975,12.322l2.79,15.575l12.554,3.953
				l5.58-4.187v-5.346l-4.65-2.324l4.42-3.022l-1.63-9.532l-6.975-5.58l10.23,3.257l8.37-3.257l-5.116,3.486l-1.627,11.626
				l4.184,3.254l-3.95,2.093l0.463,6.276l3.953,2.789l10.229-3.253l3.954-12.322l-2.79-11.855L216.976,207.81z M219.766,195.024
				l0.696-6.743l-7.206,2.557h-10.695l0.466-5.81l2.324-1.629l3.952-0.93l-3.369-0.463l-3.023,0.463l4.066-1.743l4.07-0.347
				l-5.58-0.35l-3.14,1.51l-0.464-5.58l3.487-2.323l1.163-5.58l-4.885,2.323l2.328-4.183l6.973-3.256l7.209,0.93l4.184,4.649
				l1.859,9.533l1.627,17.668L219.766,195.024z M220.579,171.776l-2.326-4.533l-8.601,1.627l-0.815,5.929l-2.091,0.464l-1.629,3.022
				l6.858-0.35l-0.113-3.37L220.579,171.776z M198.609,195.721l2.325-4.649l0.233-8.136l-0.93-12.786l3.253-5.58l7.673-3.49
				l8.369,1.164l4.417,5.813l2.559,9.996l1.627,18.135l2.323,0.463l-0.696-19.062l-3.486-10.462l-5.813-6.743l-8.833-1.627
				l-10,3.487l-3.485,8.835l0.93,10.926L198.609,195.721z M232.551,199.673l-12.785-2.557l-8.836,2.324l1.629,5.579l17.669,3.72
				l13.249,6.51l-5.113-12.089L232.551,199.673z M207.21,204.557l2.093-4.883l-12.089-2.79l-11.624,1.396l-7.672,7.439l-2.092,9.529
				l16.739-9.065L207.21,204.557z"/>
			</g>
		</g>
	</g>
</svg>
