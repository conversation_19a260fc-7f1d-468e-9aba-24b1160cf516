server {
    listen       8080;

    root /usr/share/nginx/html;

    # Force HTTPS for all redirects and links
    set $forwarded_scheme $scheme;
    if ($http_x_forwarded_proto = "https") {
        set $forwarded_scheme https;
    }

    # Handle font files with proper CORS and caching
    location ~* \.(woff|woff2|ttf|eot)$ {
        add_header Access-Control-Allow-Origin *;
        add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri =404;
    }

    # Handle CSS files with proper headers
    location ~* \.css$ {
        add_header Cache-Control "public, max-age=86400";
        add_header Content-Type "text/css";
        try_files $uri =404;
    }

    # Handle JS files with proper headers
    location ~* \.js$ {
        add_header Cache-Control "public, max-age=86400";
        add_header Content-Type "application/javascript";
        try_files $uri =404;
    }

    location ~ ^.*[^/]$ {
        try_files $uri @rewrite;
        add_header Cache-Control "no-cache, must-revalidate";
    }

    location @rewrite {
        return 302 $forwarded_scheme://$http_host$uri/;
    }

    # Handle all other static assets
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, must-revalidate";
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
