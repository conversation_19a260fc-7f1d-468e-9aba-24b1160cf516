# Compress files if so requested
gzip on;
# Old IE versions do not support Gzip compression properly.
gzip_disable "MSIE [1-6]\.";

gzip_comp_level 6;
# Don't compress small files: the overhead will exceed the gain.
gzip_min_length 1100;
gzip_buffers 16 8k;
# Proxying is expected, we can compress here and it does not need to
# be recompressed by the reverse proxy.
gzip_proxied any;
# Types, besides text/html, to compress.
gzip_types
    text/plain
    text/css
    text/js
    text/xml
    text/javascript
    application/javascript
    application/json
    application/xml
    application/rss+xml
    image/svg+xml;

# Use statically compressed files where available
gzip_static on;

# Send small files immediately
tcp_nodelay on;
