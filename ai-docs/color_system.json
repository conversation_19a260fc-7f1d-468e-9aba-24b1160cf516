{"colorSystem": {"primary": {"mayoBlue": {"name": "Mayo Blue", "hex": "#0057B8", "rgb": "rgb(0, 84, 166)", "hsl": "hsl(210, 100%, 33%)", "cmyk": "C100 M62 Y0 K0", "pantone": "PMS 2935 C/U", "useContext": "Primary brand color, main CTAs, headers, logos", "accessibilityNotes": "Use with white text for AA compliance"}, "mayoBlueDark": {"name": "Mayo Blue Dark", "hex": "#003F7F", "rgb": "rgb(0, 63, 127)", "hsl": "hsl(210, 100%, 25%)", "cmyk": "C100 M80 Y0 K0", "useContext": "Darker primary variant, hover states, emphasis", "accessibilityNotes": "Excellent contrast with white text"}, "mayoBlueLight": {"name": "Mayo Blue Light", "hex": "#4A90E2", "rgb": "rgb(74, 144, 226)", "hsl": "hsl(212, 73%, 59%)", "useContext": "Lighter primary variant, backgrounds, secondary elements"}}, "secondary": {"mayoBrightBlue": {"name": "Mayo Bright Blue", "hex": "#00A3E0", "rgb": "rgb(0, 163, 224)", "hsl": "hsl(196, 100%, 44%)", "cmyk": "C85 M6 Y0 K0", "pantone": "PMS 2925 C/U", "useContext": "Secondary brand color, highlights, interactive elements", "accessibilityNotes": "Use with white or dark text for proper contrast"}, "mayoBrightBlueLight": {"name": "Mayo Bright Blue Light", "hex": "#66C2FF", "rgb": "rgb(102, 194, 255)", "hsl": "hsl(204, 100%, 70%)", "useContext": "Light secondary variant, backgrounds, data visualization"}}, "neutral": {"white": {"name": "White", "hex": "#FFFFFF", "rgb": "rgb(255, 255, 255)", "hsl": "hsl(0, 0%, 100%)", "cmyk": "C0 M0 Y0 K0", "useContext": "Background, text on dark backgrounds, negative space", "accessibilityNotes": "Perfect contrast with dark colors"}, "black": {"name": "Black", "hex": "#000000", "rgb": "rgb(0, 0, 0)", "hsl": "hsl(0, 0%, 0%)", "cmyk": "C0 M0 Y0 K100", "pantone": "PMS BLACK 6 C/U", "useContext": "Text on light backgrounds, high contrast elements", "accessibilityNotes": "Perfect contrast with white background"}, "grayLight": {"name": "Gray Light", "hex": "#F5F5F5", "rgb": "rgb(245, 245, 245)", "hsl": "hsl(0, 0%, 96%)", "useContext": "Light backgrounds, subtle borders, cards"}, "grayMedium": {"name": "Gray Medium", "hex": "#CCCCCC", "rgb": "rgb(204, 204, 204)", "hsl": "hsl(0, 0%, 80%)", "useContext": "Borders, dividers, inactive states"}, "grayDark": {"name": "<PERSON>", "hex": "#666666", "rgb": "rgb(102, 102, 102)", "hsl": "hsl(0, 0%, 40%)", "useContext": "Secondary text, subtle elements", "accessibilityNotes": "AA compliant on white backgrounds"}}, "accent": {"teal": {"name": "<PERSON><PERSON>", "hex": "#9CDBD9", "rgb": "rgb(156, 219, 217)", "hsl": "hsl(178, 42%, 74%)", "cmyk": "C36 M0 Y14 K0", "pantone": "PMS 324 C/U", "useContext": "Data visualization, accents, icons", "accessibilityNotes": "Use with dark text for readability"}, "purple": {"name": "Purple", "hex": "#8246AF", "rgb": "rgb(130, 70, 175)", "hsl": "hsl(274, 43%, 48%)", "cmyk": "C58 M76 Y0 K0", "pantone": "PMS 2587 C/U", "useContext": "Data visualization, secondary accents, progress indicators", "accessibilityNotes": "Use with white text for proper contrast"}, "yellow": {"name": "Yellow", "hex": "#FFC845", "rgb": "rgb(255, 200, 69)", "hsl": "hsl(42, 100%, 64%)", "cmyk": "C0 M19 Y79 K0", "pantone": "PMS 1225 C/U", "useContext": "Warnings, highlights, data visualization", "accessibilityNotes": "Use with dark text for readability"}, "red": {"name": "Red", "hex": "#E40026", "rgb": "rgb(228, 0, 38)", "hsl": "hsl(350, 100%, 45%)", "cmyk": "C0 M100 Y75 K0", "pantone": "PMS 185 C/U", "useContext": "Errors, alerts, critical information", "accessibilityNotes": "Use with white text for proper contrast"}, "orange": {"name": "Orange", "hex": "#FE5000", "rgb": "rgb(254, 80, 0)", "hsl": "hsl(19, 100%, 50%)", "cmyk": "C0 M65 Y100 K0", "pantone": "PMS ORANGE 021 C/U", "useContext": "Warnings, secondary CTAs, data visualization", "accessibilityNotes": "Use with white text for proper contrast"}, "green": {"name": "Green", "hex": "#008756", "rgb": "rgb(0, 135, 86)", "hsl": "hsl(158, 100%, 26%)", "cmyk": "C100 M0 Y97 K13", "pantone": "PMS 2418 C/U", "useContext": "Success states, confirmations, positive indicators", "accessibilityNotes": "Use with white text for proper contrast"}}, "semantic": {"success": {"name": "Success", "hex": "#008756", "rgb": "rgb(0, 135, 86)", "hsl": "hsl(158, 100%, 26%)", "useContext": "Success messages, confirmations, positive feedback", "accessibilityNotes": "AA compliant with white text"}, "warning": {"name": "Warning", "hex": "#FFC845", "rgb": "rgb(255, 200, 69)", "hsl": "hsl(42, 100%, 64%)", "useContext": "Warning messages, caution states", "accessibilityNotes": "Use with dark text for readability"}, "error": {"name": "Error", "hex": "#E40026", "rgb": "rgb(228, 0, 38)", "hsl": "hsl(350, 100%, 45%)", "useContext": "Error messages, validation errors, critical alerts", "accessibilityNotes": "AA compliant with white text"}, "info": {"name": "Info", "hex": "#00A3E0", "rgb": "rgb(0, 163, 224)", "hsl": "hsl(196, 100%, 44%)", "useContext": "Informational messages, help text, notifications", "accessibilityNotes": "AA compliant with white text"}}, "guidelines": {"usage": {"primaryRatio": "70% blue and white, 30% black", "accentLimit": "Maximum of two accent colors together with primary palette", "accessibility": "All color combinations must meet WCAG AA standards (4.5:1 contrast ratio)", "dataVisualization": "Use accent colors for clarity in charts and graphs"}, "accessibility": {"minimumContrast": "4.5:1 for normal text, 3:1 for large text (18pt+ or 14pt+ bold)", "colorBlindness": "Do not rely solely on color to convey information", "testing": "Test all color combinations with accessibility tools"}, "application": {"backgrounds": "Primarily white with blue accents", "text": "Black on light backgrounds, white on dark backgrounds", "interactive": "Use blue variants for interactive elements", "status": "Use semantic colors for system feedback"}}}}