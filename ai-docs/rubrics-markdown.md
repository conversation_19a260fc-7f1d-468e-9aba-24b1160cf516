# Mayo Clinic Platform UI/UX Evaluation Rubrics

## Version Information
- **Version**: 1.0
- **Brand**: Mayo Clinic Platform
- **Last Updated**: 2024-01-01
- **Purpose**: AI-consumable evaluation criteria for validating UI components against Mayo Clinic Platform brand standards

---

## Color System Rubrics

### COLOR-001: Primary Brand Color Adherence
- **Principle**: Primary Brand Color Adherence
- **Guideline**: Primary background colors must use Mayo Blue (#0057B8) or Mayo Bright Blue (#00A3E0) from the brand palette
- **Metric Type**: `colorMatch`
- **Target Elements**: `backgrounds.primary, headers.main, buttons.primary`
- **Expected Value Logic**: `backgroundColor IN ['#0057B8', '#00A3E0']`
- **Validation Query**: "Is the primary background color exactly #0057B8 (Mayo Blue) or #00A3E0 (Mayo Bright Blue)?"
- **Severity**: CRITICAL

### COLOR-002: Color Ratio Compliance
- **Principle**: Color Ratio Compliance
- **Guideline**: The ratio of blue and white to black should be 70/30 across the entire component
- **Metric Type**: `colorRatio`
- **Target Elements**: `component.entireLayout`
- **Expected Value Logic**: `(blueArea + whiteArea) / blackArea >= 2.33`
- **Validation Query**: "Does the combined area of blue and white elements constitute at least 70% of the total colored area?"
- **Severity**: MAJOR

### COLOR-003: Text Color Contrast
- **Principle**: Text Color Contrast
- **Guideline**: Text on Mayo Blue backgrounds must be white (#FFFFFF) for AA compliance
- **Metric Type**: `contrastRatio`
- **Target Elements**: `text.onPrimaryBlue`
- **Expected Value Logic**: `color: '#FFFFFF' AND contrastRatio >= 4.5:1`
- **Validation Query**: "Is white text used on Mayo Blue backgrounds with a contrast ratio of at least 4.5:1?"
- **Severity**: CRITICAL

### COLOR-004: Accent Color Limitation
- **Principle**: Accent Color Limitation
- **Guideline**: Maximum of two accent colors can be used together with the primary palette
- **Metric Type**: `colorCount`
- **Target Elements**: `component.allElements`
- **Expected Value Logic**: `accentColorCount <= 2`
- **Validation Query**: "Are there no more than 2 accent colors (teal, purple, yellow, red, orange, green) used alongside primary colors?"
- **Severity**: MAJOR

---

## Typography Rubrics

### TYPO-001: Heading Typography Hierarchy
- **Principle**: Heading Typography Hierarchy
- **Guideline**: H1 headings must use Mayo Clinic Sans Bold at 36px for digital
- **Metric Type**: `fontSizeCheck`
- **Target Elements**: `headings.h1`
- **Expected Value Logic**: `fontFamily: 'Mayo Clinic Sans' AND fontWeight: 'Bold' AND fontSize: '36px'`
- **Validation Query**: "Are H1 elements using Mayo Clinic Sans Bold at exactly 36px?"
- **Severity**: CRITICAL

### TYPO-002: Body Text Readability
- **Principle**: Body Text Readability
- **Guideline**: Body text must be 16-21px for digital, with line height 1.4-1.6
- **Metric Type**: `fontSizeCheck`
- **Target Elements**: `text.body, paragraphs`
- **Expected Value Logic**: `fontSize >= 16px AND fontSize <= 21px AND lineHeight >= 1.4 AND lineHeight <= 1.6`
- **Validation Query**: "Is body text between 16-21px with line height between 1.4-1.6?"
- **Severity**: MAJOR

### TYPO-003: Line Length Constraint
- **Principle**: Line Length Constraint
- **Guideline**: Text line length must not exceed 75 characters including spaces
- **Metric Type**: `lineLength`
- **Target Elements**: `text.paragraphs, text.body`
- **Expected Value Logic**: `characterCount <= 75`
- **Validation Query**: "Are all text lines 75 characters or fewer including spaces?"
- **Severity**: MAJOR

### TYPO-004: Font Family Consistency
- **Principle**: Font Family Consistency
- **Guideline**: All text must use Mayo Clinic Sans with system-ui fallback
- **Metric Type**: `fontFamilyCheck`
- **Target Elements**: `text.all`
- **Expected Value Logic**: `fontFamily: 'Mayo Clinic Sans, system-ui, -apple-system, sans-serif'`
- **Validation Query**: "Is Mayo Clinic Sans specified as the primary font with proper fallbacks?"
- **Severity**: CRITICAL

---

## Layout Rubrics

### LAYOUT-001: Grid System Compliance
- **Principle**: Grid System Compliance
- **Guideline**: Desktop layouts must use 12-column grid with 18px gutters and 36px margins
- **Metric Type**: `layoutAlignment`
- **Target Elements**: `layout.desktop`
- **Expected Value Logic**: `columns: 12 AND gutter: '18px' AND margin: '36px'`
- **Validation Query**: "Does the desktop layout use a 12-column grid with 18px gutters and 36px margins?"
- **Severity**: MAJOR

### LAYOUT-002: Mobile Responsiveness
- **Principle**: Mobile Responsiveness
- **Guideline**: Mobile layouts must use 2-column grid with 18px gutters and 36px margins
- **Metric Type**: `layoutAlignment`
- **Target Elements**: `layout.mobile`
- **Expected Value Logic**: `columns: 2 AND gutter: '18px' AND margin: '36px'`
- **Validation Query**: "Does the mobile layout use a 2-column grid with proper spacing?"
- **Severity**: MAJOR

### LAYOUT-003: Negative Space Utilization
- **Principle**: Negative Space Utilization
- **Guideline**: Components must include adequate white space as a design element
- **Metric Type**: `spacingCheck`
- **Target Elements**: `component.contentAreas`
- **Expected Value Logic**: `padding >= '18px' OR margin >= '18px'`
- **Validation Query**: "Is there sufficient negative space (at least 18px) around content areas?"
- **Severity**: MINOR

---

## Brand Element Rubrics

### BRAND-001: Underscore Element Usage
- **Principle**: Underscore Element Usage
- **Guideline**: The underscore design element must appear in Mayo Blue, black, or white only
- **Metric Type**: `colorMatch`
- **Target Elements**: `elements.underscore`
- **Expected Value Logic**: `color IN ['#0057B8', '#000000', '#FFFFFF']`
- **Validation Query**: "If an underscore element is present, is it colored in Mayo Blue, black, or white?"
- **Severity**: MAJOR

### BRAND-002: Logo Clear Space
- **Principle**: Logo Clear Space
- **Guideline**: Mayo Clinic Platform logo must have clear space equal to the height of capital letters (Y dimension)
- **Metric Type**: `spacingCheck`
- **Target Elements**: `logo.mayoClinicPlatform`
- **Expected Value Logic**: `clearSpace >= logoCapitalLetterHeight`
- **Validation Query**: "Does the logo have clear space at least equal to the height of its capital letters on all sides?"
- **Severity**: CRITICAL

---

## Component Rubrics

### COMPONENT-001: Button State Consistency
- **Principle**: Button State Consistency
- **Guideline**: Primary buttons must have defined hover, focus, and disabled states
- **Metric Type**: `propertyExists`
- **Target Elements**: `buttons.primary`
- **Expected Value Logic**: `hasHoverState AND hasFocusState AND hasDisabledState`
- **Validation Query**: "Do primary buttons have properly defined hover, focus, and disabled states?"
- **Severity**: MAJOR

### COMPONENT-002: Icon Style Compliance
- **Principle**: Icon Style Compliance
- **Guideline**: Icons must be flat, without effects, borders, or drop shadows
- **Metric Type**: `styleCheck`
- **Target Elements**: `icons.all`
- **Expected Value Logic**: `boxShadow: 'none' AND border: 'none' AND filter: 'none'`
- **Validation Query**: "Are all icons flat without shadows, borders, or visual effects?"
- **Severity**: MINOR

---

## Accessibility Rubrics

### ACCESS-001: Touch Target Size
- **Principle**: Touch Target Size
- **Guideline**: Interactive elements must have minimum touch target of 44x44px for mobile
- **Metric Type**: `sizeCheck`
- **Target Elements**: `interactive.mobile`
- **Expected Value Logic**: `minWidth >= 44px AND minHeight >= 44px`
- **Validation Query**: "Are all interactive elements at least 44x44px on mobile devices?"
- **Severity**: MAJOR

### ACCESS-002: Color Contrast Compliance
- **Principle**: Color Contrast Compliance
- **Guideline**: All text must meet WCAG AA standards (4.5:1 for normal text, 3:1 for large text)
- **Metric Type**: `contrastRatio`
- **Target Elements**: `text.all`
- **Expected Value Logic**: `(fontSize < 18px AND contrastRatio >= 4.5) OR (fontSize >= 18px AND contrastRatio >= 3)`
- **Validation Query**: "Does all text meet WCAG AA contrast requirements?"
- **Severity**: CRITICAL

### ACCESS-003: Focus Indicator Visibility
- **Principle**: Focus Indicator Visibility
- **Guideline**: All interactive elements must have visible focus indicators
- **Metric Type**: `styleCheck`
- **Target Elements**: `interactive.all:focus`
- **Expected Value Logic**: `outline !== 'none' OR boxShadow !== 'none'`
- **Validation Query**: "Do all interactive elements show visible focus indicators?"
- **Severity**: CRITICAL

---

## Semantic Rubrics

### SEMANTIC-001: Semantic Color Usage
- **Principle**: Semantic Color Usage
- **Guideline**: Success states must use green (#008756), errors must use red (#E40026)
- **Metric Type**: `colorMatch`
- **Target Elements**: `states.feedback`
- **Expected Value Logic**: `(state === 'success' AND color === '#008756') OR (state === 'error' AND color === '#E40026')`
- **Validation Query**: "Are semantic colors correctly applied (green for success, red for errors)?"
- **Severity**: MAJOR

---

## Media Rubrics

### IMAGERY-001: Image Quality Standards
- **Principle**: Image Quality Standards
- **Guideline**: Images must be clear, focused, and avoid clichéd healthcare imagery
- **Metric Type**: `qualityCheck`
- **Target Elements**: `images.all`
- **Expected Value Logic**: `resolution >= 72dpi AND aspectRatioPreserved === true`
- **Validation Query**: "Are all images high quality and properly sized without distortion?"
- **Severity**: MINOR

---

## Validation Helpers Reference

### Color Palettes
- **Mayo Blue Variations**: `#0057B8`, `#003F7F`, `#4A90E2`
- **Mayo Bright Blue Variations**: `#00A3E0`, `#66C2FF`
- **Neutral Colors**: White (`#FFFFFF`), Black (`#000000`)
- **Accent Colors**: 
  - Teal: `#9CDBD9`
  - Purple: `#8246AF`
  - Yellow: `#FFC845`
  - Red: `#E40026`
  - Orange: `#FE5000`
  - Green: `#008756`

### Typography Standards
- **Primary Font**: Mayo Clinic Sans
- **Fallback Fonts**: system-ui, -apple-system, sans-serif
- **Digital Font Sizes**:
  - H1: 36px
  - H2: 24-35px
  - H3: 18-23px
  - Body: 16-21px
  - Caption: 12-14px

### Grid System Breakpoints
- **Mobile**: 375px (2 columns)
- **Tablet**: 768px (4 columns)
- **Laptop**: 1440px (8 columns)
- **Desktop**: 1920px (12 columns)
- **Standard Gutter**: 18px
- **Standard Margin**: 36px

### Semantic Color Mapping
- **Success**: `#008756` (Green)
- **Warning**: `#FFC845` (Yellow)
- **Error**: `#E40026` (Red)
- **Info**: `#00A3E0` (Mayo Bright Blue)

---

## Usage Notes for AI Coding LLM

1. **Validation Order**: Process CRITICAL severity items first, then MAJOR, then MINOR
2. **Cascade Rules**: Some rubrics may override others (e.g., semantic colors override general color rules)
3. **Context Awareness**: Some rubrics apply only to specific viewport sizes or component states
4. **Framework Agnostic**: These rubrics should be interpreted regardless of the UI framework being used
5. **Partial Compliance**: Document any rubrics that cannot be fully validated due to technical limitations

## Severity Definitions
- **CRITICAL**: Must be fixed before component is usable
- **MAJOR**: Should be fixed for brand compliance
- **MINOR**: Nice to have for perfect brand alignment