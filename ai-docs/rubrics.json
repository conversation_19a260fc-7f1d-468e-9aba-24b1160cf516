{"mayo_clinic_platform_ui_ux_rubrics": {"version": "1.0", "brand": "Mayo Clinic Platform", "last_updated": "2024-01-01", "rubrics": [{"id": "COLOR-001", "principle": "Primary Brand Color Adherence", "guideline": "Primary background colors must use Mayo Blue (#0057B8) or Mayo Bright Blue (#00A3E0) from the brand palette", "metric_type": "colorMatch", "target_element_selector_hint": "backgrounds.primary, headers.main, buttons.primary", "expected_value_logic": "backgroundColor IN ['#0057B8', '#00A3E0']", "validation_query_suggestion": "Is the primary background color exactly #0057B8 (Mayo Blue) or #00A3E0 (Mayo Bright Blue)?", "severity": "critical"}, {"id": "COLOR-002", "principle": "Color Ratio Compliance", "guideline": "The ratio of blue and white to black should be 70/30 across the entire component", "metric_type": "colorRatio", "target_element_selector_hint": "component.entireLayout", "expected_value_logic": "(blueArea + whiteArea) / blackArea >= 2.33", "validation_query_suggestion": "Does the combined area of blue and white elements constitute at least 70% of the total colored area?", "severity": "major"}, {"id": "COLOR-003", "principle": "Text Color Contrast", "guideline": "Text on Mayo Blue backgrounds must be white (#FFFFFF) for AA compliance", "metric_type": "contrastRatio", "target_element_selector_hint": "text.onPrimaryBlue", "expected_value_logic": "color: '#FFFFFF' AND contrastRatio >= 4.5:1", "validation_query_suggestion": "Is white text used on Mayo Blue backgrounds with a contrast ratio of at least 4.5:1?", "severity": "critical"}, {"id": "COLOR-004", "principle": "Accent Color Limitation", "guideline": "Maximum of two accent colors can be used together with the primary palette", "metric_type": "colorCount", "target_element_selector_hint": "component.allElements", "expected_value_logic": "accentColorCount <= 2", "validation_query_suggestion": "Are there no more than 2 accent colors (teal, purple, yellow, red, orange, green) used alongside primary colors?", "severity": "major"}, {"id": "TYPO-001", "principle": "Heading Typography Hierarchy", "guideline": "H1 headings must use Mayo Clinic Sans Bold at 36px for digital", "metric_type": "fontSizeCheck", "target_element_selector_hint": "headings.h1", "expected_value_logic": "fontFamily: 'Mayo Clinic Sans' AND fontWeight: 'Bold' AND fontSize: '36px'", "validation_query_suggestion": "Are H1 elements using Mayo Clinic Sans Bold at exactly 36px?", "severity": "critical"}, {"id": "TYPO-002", "principle": "Body Text Readability", "guideline": "Body text must be 16-21px for digital, with line height 1.4-1.6", "metric_type": "fontSizeCheck", "target_element_selector_hint": "text.body, paragraphs", "expected_value_logic": "fontSize >= 16px AND fontSize <= 21px AND lineHeight >= 1.4 AND lineHeight <= 1.6", "validation_query_suggestion": "Is body text between 16-21px with line height between 1.4-1.6?", "severity": "major"}, {"id": "TYPO-003", "principle": "Line Length Constraint", "guideline": "Text line length must not exceed 75 characters including spaces", "metric_type": "lineLength", "target_element_selector_hint": "text.paragraphs, text.body", "expected_value_logic": "characterCount <= 75", "validation_query_suggestion": "Are all text lines 75 characters or fewer including spaces?", "severity": "major"}, {"id": "TYPO-004", "principle": "Font Family Consistency", "guideline": "All text must use Mayo Clinic Sans with system-ui fallback", "metric_type": "fontFamilyCheck", "target_element_selector_hint": "text.all", "expected_value_logic": "fontFamily: 'Mayo Clinic Sans, system-ui, -apple-system, sans-serif'", "validation_query_suggestion": "Is Mayo Clinic Sans specified as the primary font with proper fallbacks?", "severity": "critical"}, {"id": "LAYOUT-001", "principle": "Grid System Compliance", "guideline": "Desktop layouts must use 12-column grid with 18px gutters and 36px margins", "metric_type": "layoutAlignment", "target_element_selector_hint": "layout.desktop", "expected_value_logic": "columns: 12 AND gutter: '18px' AND margin: '36px'", "validation_query_suggestion": "Does the desktop layout use a 12-column grid with 18px gutters and 36px margins?", "severity": "major"}, {"id": "LAYOUT-002", "principle": "Mobile Responsiveness", "guideline": "Mobile layouts must use 2-column grid with 18px gutters and 36px margins", "metric_type": "layoutAlignment", "target_element_selector_hint": "layout.mobile", "expected_value_logic": "columns: 2 AND gutter: '18px' AND margin: '36px'", "validation_query_suggestion": "Does the mobile layout use a 2-column grid with proper spacing?", "severity": "major"}, {"id": "LAYOUT-003", "principle": "Negative Space Utilization", "guideline": "Components must include adequate white space as a design element", "metric_type": "spacingCheck", "target_element_selector_hint": "component.contentAreas", "expected_value_logic": "padding >= '18px' OR margin >= '18px'", "validation_query_suggestion": "Is there sufficient negative space (at least 18px) around content areas?", "severity": "minor"}, {"id": "BRAND-001", "principle": "Underscore Element Usage", "guideline": "The underscore design element must appear in Mayo Blue, black, or white only", "metric_type": "colorMatch", "target_element_selector_hint": "elements.underscore", "expected_value_logic": "color IN ['#0057B8', '#000000', '#FFFFFF']", "validation_query_suggestion": "If an underscore element is present, is it colored in Mayo Blue, black, or white?", "severity": "major"}, {"id": "BRAND-002", "principle": "Logo Clear Space", "guideline": "Mayo Clinic Platform logo must have clear space equal to the height of capital letters (Y dimension)", "metric_type": "spacingCheck", "target_element_selector_hint": "logo.mayoClinicPlatform", "expected_value_logic": "clearSpace >= logoCapitalLetterHeight", "validation_query_suggestion": "Does the logo have clear space at least equal to the height of its capital letters on all sides?", "severity": "critical"}, {"id": "COMPONENT-001", "principle": "Button State Consistency", "guideline": "Primary buttons must have defined hover, focus, and disabled states", "metric_type": "propertyExists", "target_element_selector_hint": "buttons.primary", "expected_value_logic": "hasHoverState AND hasFocusState AND hasDisabledState", "validation_query_suggestion": "Do primary buttons have properly defined hover, focus, and disabled states?", "severity": "major"}, {"id": "COMPONENT-002", "principle": "Icon Style Compliance", "guideline": "Icons must be flat, without effects, borders, or drop shadows", "metric_type": "styleCheck", "target_element_selector_hint": "icons.all", "expected_value_logic": "boxShadow: 'none' AND border: 'none' AND filter: 'none'", "validation_query_suggestion": "Are all icons flat without shadows, borders, or visual effects?", "severity": "minor"}, {"id": "ACCESS-001", "principle": "Touch Target Size", "guideline": "Interactive elements must have minimum touch target of 44x44px for mobile", "metric_type": "sizeCheck", "target_element_selector_hint": "interactive.mobile", "expected_value_logic": "minWidth >= 44px AND minHeight >= 44px", "validation_query_suggestion": "Are all interactive elements at least 44x44px on mobile devices?", "severity": "major"}, {"id": "ACCESS-002", "principle": "Color Contrast Compliance", "guideline": "All text must meet WCAG AA standards (4.5:1 for normal text, 3:1 for large text)", "metric_type": "contrastRatio", "target_element_selector_hint": "text.all", "expected_value_logic": "(fontSize < 18px AND contrastRatio >= 4.5) OR (fontSize >= 18px AND contrastRatio >= 3)", "validation_query_suggestion": "Does all text meet WCAG AA contrast requirements?", "severity": "critical"}, {"id": "ACCESS-003", "principle": "Focus Indicator Visibility", "guideline": "All interactive elements must have visible focus indicators", "metric_type": "styleCheck", "target_element_selector_hint": "interactive.all:focus", "expected_value_logic": "outline !== 'none' OR boxShadow !== 'none'", "validation_query_suggestion": "Do all interactive elements show visible focus indicators?", "severity": "critical"}, {"id": "SEMANTIC-001", "principle": "Semantic Color Usage", "guideline": "Success states must use green (#008756), errors must use red (#E40026)", "metric_type": "colorMatch", "target_element_selector_hint": "states.feedback", "expected_value_logic": "(state === 'success' AND color === '#008756') OR (state === 'error' AND color === '#E40026')", "validation_query_suggestion": "Are semantic colors correctly applied (green for success, red for errors)?", "severity": "major"}, {"id": "IMAGERY-001", "principle": "Image Quality Standards", "guideline": "Images must be clear, focused, and avoid clichéd healthcare imagery", "metric_type": "qualityCheck", "target_element_selector_hint": "images.all", "expected_value_logic": "resolution >= 72dpi AND aspectRatioPreserved === true", "validation_query_suggestion": "Are all images high quality and properly sized without distortion?", "severity": "minor"}], "validation_helpers": {"mayo_blue_variations": ["#0057B8", "#003F7F", "#4A90E2"], "mayo_bright_blue_variations": ["#00A3E0", "#66C2FF"], "acceptable_fonts": ["Mayo Clinic Sans", "system-ui", "-apple-system", "sans-serif"], "grid_breakpoints": {"mobile": 375, "tablet": 768, "laptop": 1440, "desktop": 1920}, "accent_colors": {"teal": "#9CDBD9", "purple": "#8246AF", "yellow": "#FFC845", "red": "#E40026", "orange": "#FE5000", "green": "#008756"}, "semantic_colors": {"success": "#008756", "warning": "#FFC845", "error": "#E40026", "info": "#00A3E0"}}}}