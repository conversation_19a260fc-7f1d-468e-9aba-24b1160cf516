{"typography": {"fontFamily": {"primary": "Mayo Clinic Sans", "fallback": "system-ui, -apple-system, sans-serif"}, "elements": {"heading1": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "36pt", "digital": "36px"}, "fontWeight": "Bold", "lineHeight": "Auto", "letterSpacing": "Reduced for large sizes", "useContext": "Primary headlines, major section headers", "notes": "Reduce leading and tracking at larger sizes for correct visual expression"}, "heading2": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "24-35pt", "digital": "24-35px"}, "fontWeight": "Bold", "lineHeight": "Auto", "letterSpacing": "Normal", "useContext": "Secondary headlines, subsection headers"}, "heading3": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "18-23pt", "digital": "18-23px"}, "fontWeight": "Bold", "lineHeight": "Auto", "letterSpacing": "Normal", "useContext": "Tertiary headlines, component headers"}, "bodyText": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "9-11pt", "digital": "16-21px"}, "fontWeight": "Regular", "lineHeight": "1.4-1.6", "letterSpacing": "Normal", "maxLineLength": "75 characters including spaces", "useContext": "All body copy, paragraphs, general text content", "notes": "Leading often proportionately increases at smaller sizes to maximize legibility"}, "midSizeTypography": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "12-35pt", "digital": "22-35px"}, "fontWeight": "Regular", "lineHeight": "Auto", "letterSpacing": "Normal", "useContext": "Intermediate text sizes, callouts, important secondary text"}, "caption": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "8-9pt", "digital": "12-14px"}, "fontWeight": "Regular", "lineHeight": "1.3-1.4", "letterSpacing": "Normal", "useContext": "Image captions, small descriptive text, metadata"}, "label": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "9-10pt", "digital": "14-16px"}, "fontWeight": "Medium", "lineHeight": "1.2-1.3", "letterSpacing": "Normal", "useContext": "Form labels, UI element labels, navigation items"}, "button": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": "10-12pt", "digital": "16-18px"}, "fontWeight": "Medium", "lineHeight": "1.2", "letterSpacing": "Normal", "useContext": "Button text, calls-to-action"}, "displayLarge": {"fontFamily": "Mayo Clinic Sans", "fontSize": {"desktop": ">36pt", "digital": ">36px"}, "fontWeight": "Bold", "lineHeight": "0.9-1.1", "letterSpacing": "Reduced", "useContext": "Hero headlines, major impact statements", "notes": "Significant reduction in leading and tracking required"}}, "guidelines": {"accessibility": {"minimumContrast": "WCAG AA compliant (4.5:1 for normal text, 3:1 for large text)", "readability": "Optimal line length is 50-60 characters, maximum 75 characters including spaces"}, "usage": {"headlineStyle": "Sentence case for titles and subheadings", "emphasis": "Use heavier weights sparingly for important subtitles, headers, or product names", "whitespace": "Negative space is a design element - give content space to breathe", "hierarchy": "Create visual hierarchy through typographic contrast"}, "responsive": {"mobile": {"columns": 2, "gutter": "18px", "margin": "36px"}, "tablet": {"columns": 4, "gutter": "18px", "margin": "36px"}, "laptop": {"columns": 8, "gutter": "18px", "margin": "36px"}, "desktop": {"columns": 12, "gutter": "18px", "margin": "36px"}}}}}