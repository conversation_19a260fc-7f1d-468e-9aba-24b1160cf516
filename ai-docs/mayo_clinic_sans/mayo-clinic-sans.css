@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-Black"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Black.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Black.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Black.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: normal;
  font-weight: 900;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-Bold"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Bold.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Bold.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Bold.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: normal;
  font-weight: bold;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-BoldItalic"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-BoldItalic.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-BoldItalic.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-BoldItalic.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: italic;
  font-weight: bold;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-Light"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Light.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Light.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Light.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: normal;
  font-weight: 300;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-LightItalic"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-LightItalic.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-LightItalic.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-LightItalic.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: italic;
  font-weight: 300;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-Medium"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Medium.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Medium.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Medium.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: normal;
  font-weight: 500;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-Regular"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Regular.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Regular.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-Regular.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: normal;
  font-weight: normal;
  font-display: swap;
}
@font-face {
  font-family: "Mayo Clinic Sans";
  src: local("MayoClinicSans-RegularItalic"),
       url("./fonts/mayo-clinic-sans/MayoClinicSans-RegularItalic.woff2") format("woff2"),  /* chrome 36+, firefox 39+,iOS 10+, Android 67+ */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-RegularItalic.woff") format("woff"),    /* chrome, firefox */
       url("./fonts/mayo-clinic-sans/MayoClinicSans-RegularItalic.ttf") format("truetype"); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  font-style: italic;
  font-weight: normal;
  font-display: swap;
}