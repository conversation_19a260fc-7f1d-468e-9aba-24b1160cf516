# Continuous integration
name: CI

# Run in master and dev branches and in all pull requests to those branches
on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

env:
  DOCKER_IMAGE: ohdsi/atlas

jobs:
  # Build and test the code
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Caches NPM dependencies, as long as the package-lock.json is not modified
      - uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Build code
        run: npm run build

  # Check that the docker image builds correctly
  # Push to ohdsi/atlas:master for commits on master.
  docker:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      # Add Docker labels and tags
      - name: Docker meta
        id: docker_meta
        uses: crazy-max/ghaction-docker-meta@v1
        with:
          images: ${{ env.DOCKER_IMAGE }}

      # Setup docker build environment
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v1
        if: ${{ github.event_name != 'pull_request' }}
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: ./
          file: ./Dockerfile
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache,mode=max
          push: ${{ github.event_name != 'pull_request' }}  # Push the master branch only
          load: ${{ github.event_name == 'pull_request' }}  # Otherwise, just load it locally
          tags: ${{ steps.docker_meta.outputs.tags }}
          # Use runtime labels from docker_meta as well as fixed labels
          labels: |
            ${{ steps.docker_meta.outputs.labels }}
            maintainer=Joris Borgdorff <<EMAIL>>, Lee Evans - www.ltscomputingllc.com
            org.opencontainers.image.authors=Joris Borgdorff <<EMAIL>>, Lee Evans - www.ltscomputingllc.com, Shaun Turner <<EMAIL>>
            org.opencontainers.image.vendor=OHDSI

      # If the image was pushed, we need to pull it again to inspect it
      - name: Pull image
        if: ${{ github.event_name != 'pull_request' }}
        run: docker pull ${{ env.DOCKER_IMAGE }}:${{ steps.docker_meta.outputs.version }}

      - name: Inspect image
        run: |
          docker image inspect ${{ env.DOCKER_IMAGE }}:${{ steps.docker_meta.outputs.version }}
