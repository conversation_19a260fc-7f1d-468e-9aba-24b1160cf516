<linked-entity-list params="
	title: $component.title,
	descr: $component.descr,
	newItemLabel: $component.newItemLabel,
	newItemAction: $component.newItemAction,
	data: $component.data,
	columns: $component.columns,
	removeCohort: removeCohort,
	isEditPermitted: $component.isEditPermitted
"></linked-entity-list>

<atlas-modal params="
	showModal: $component.showModal,
	dialogExtraClasses: [ $component.classes('cohort-definitions-modal') ],
	title: ko.i18n('components.linkedCohortList.dialog.title', 'Choose a Cohort definition'),
	data: {
		cohortSelected: $component.cohortSelected,
		classes: $component.classes,
		data: $component.data,
		showModal: $component.showModal,
		multiChoice: $component.multiChoice,
	}
">
<div class="import-modal_list-container">
	<cohort-definition-browser
		data-bind="css: classes('modal-cohort-list')"
		params="onSelect: data => cohortSelected(data), selectedData: data, showModal: showModal, multiChoice: multiChoice">
	</cohort-definition-browser>
</div>
</atlas-modal>