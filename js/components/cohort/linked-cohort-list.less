.linked-cohort-list {
	&__col-cohort-id {
		width: 10%;
	}

	&__col-cohort-name {
		width: 70%;

		&--editable {
			span {
				cursor: pointer;
				color: #0070dd;
				border-bottom: 1px dashed;
			}
			input {
				width: 100%;
			}
		}
	}

	&__col-cohort-edit, &__col-cohort-remove {
		width: 10%;
	}

	&__modal-cohort-list {

		.dt-btn {
			display: none;
		}

		.faceted-data-table-wrapper {
			padding-left: 1rem;
		}
	}

	&__cohort-definitions-modal {
		width: 100rem;
	}
}