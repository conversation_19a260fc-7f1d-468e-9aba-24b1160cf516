<div class="paddedWrapper">
	<!-- contenteditableSwitch must be placed AFTER contentEditable to override 'contenteditable' attribute -->
	<div class="divtext cohort-description" data-bind="contentEditable: currentCohortDefinition().description, contenteditableSwitch: canEdit(), placeholder: ko.i18n('components.atlasCohortEditor.enterCohortPlaceholder', 'Enter the cohort definition description here')"></div>
	<div data-bind="eventListener: [
									{ event: 'click', selector: '.conceptset_import', callback: handleConceptSetImport},
									{ event: 'click', selector: '.conceptset_edit', callback: handleEditConceptSet}
									]">
		<!-- ko if:canEdit-->
		<cohort-expression-editor params="expression: $component.currentCohortDefinition().expression, widget: $component.cohortExpressionEditor"></cohort-expression-editor>
		<!-- /ko -->
		ko ifnot:canEdit
		<cohort-expression-viewer params="expression: $component.currentCohortDefinition().expression"></cohort-expression-viewer>
		<!-- /ko -->
		<div class="modal fade" data-bind="modal: showModal" tabindex="-1" role="dialog">
			<!-- ko if:showModal-->
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header" data-bind="text: ko.i18n('components.atlasCohortEditor.importConceptSet', 'Import Concept Set From Repository...')"></div>
					<div class="paddedWrapper">
						<concept-set-browser params="
							criteriaContext: $component.criteriaContext,
							cohortConceptSets: $component.currentCohortDefinition().expression().ConceptSets,
							onActionComplete: $component.onAtlasConceptSetSelectAction,
							buttonActionEnabled: true,
							pageLength: $component.tableOptions.pageLength,
							lengthMenu: $component.tableOptions.lengthMenu,
							"></concept-set-browser>
					</div>
				</div>
			</div>
			<!-- /ko -->
		</div>
	</div>
</div>
