<div data-bind="if:$component.canReadConceptsets">

    <div data-bind="visible:$component.selectedSource().url != null">
        <!-- ko if:  loading() || isProcessing() -->
			<loading></loading>
        <!-- /ko -->

        <div data-bind="visible: !loading() && !isProcessing()">
            <div class="conceptset-browser-panel" data-bind="visible: buttonActionEnabled">
                <div class="btn btn-sm btn-primary new-concept-set"
                     data-bind="click: addConceptSet, css: {disabled: disableConceptSetButton}, text: buttonActionText"></div>
            </div>
            <faceted-datatable params="
                dataTableId: repositoryConceptSetTableId,
                orderColumn: 3,
                reference: repositoryConceptSets(),
                columns: columns,
                options: options,
                language: ko.i18n('datatable.language'),
                autoWidth: false,
                orderClasses: false,
                deferRender: true,
                pageLength: pageLength,
                lengthMenu: lengthMenu,
                rowClick: selectRepositoryConceptSet">
            </faceted-datatable>
        </div>
    </div>
</div>