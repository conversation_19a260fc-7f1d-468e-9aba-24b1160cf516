<div data-bind="visible: !isInitialLoading(), eventListener: [{event:'click', selector: '.btn-fix', callback: onFixCallback }]">
		<div data-bind="css: classes()">
				<faceted-datatable params="{
						columns: warningsColumns,
						options: warningsOptions,
						reference: warnings,
						pageLength: tableOptions.pageLength,
						lengthMenu: tableOptions.lengthMenu,
						order: [],
						language: ko.i18n('datatable.language')
				}"></faceted-datatable>
		</div>
</div>
<loading data-bind="visible: isInitialLoading()" params="status: ko.i18n('components.checks.runningDiagnostics', 'Running Diagnostics')"></loading>
