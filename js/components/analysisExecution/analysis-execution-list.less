.analysis-execution-list {
  &__title {
    margin: 0;
    margin-top: 1rem;
    font-size: 1.8rem;
  }

  &__content {
    margin-top: 1rem;
  }

  &__only-results-checkbox {
    margin-top: 5px;
  }

  &__execution-groups-table {
    &.dataTable.no-footer {
      border: none !important;
      thead {
        display: none;
      }
      td {
        padding: 0;

        & > span:first-child {
          display: none;
        }
      }
    }
  }

  &__group {
    margin: .4rem 0;

    &--expanded {
      .analysis-execution-list {
        &__heading {
          border-bottom-right-radius: 0;
          border-bottom-left-radius: 0;
        }

        &__action:last-child .btn {
          border-bottom-right-radius: 0;
        }

        &__result-list {
          display: block;
        }
      }
    }
  }

  &__heading {
    background: #e2e2e2;//#eee;
    border: 1px solid #ccc;
    border-radius: 3px;
    display: flex;
    padding: 0 0 0 1rem;
  }

  &__ds-title {
    align-self: center;
    font-weight: 500;
    font-size: 1.3rem;
    margin: 0;
  }

  &__action-list {
    display: flex;
    list-style: none;
    margin: 0;
    margin-left: auto;
  }

  &__action {
    .btn {
      border-radius: 0;
    }
    &:last-child .btn {
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
    }
  }

  &__action-text {
    padding-left: .5rem;
  }

  &__result-list {
    display: none;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    list-style: none;
    padding: 0;
  }

  &__result-line {
    .dataTables_wrapper {
      background-color: #f5f5f5;
      margin: 0;
      border: 1px solid #ccc;
      border-top: none;
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
    }

    .dataTables_length, .dataTables_filter {
      display: none;
    }

    .dataTables_info, .dataTables_paginate {
      padding-bottom: 0.75rem;
    }

    .dataTables_info {
      padding-left: 1rem;
    }

    .dataTables_paginate {
      padding-top: .75rem;
      padding-right: 1rem;
    }

    th {
      line-height: 1.9rem !important;
    }
  }

  &__result-table {

    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 1px solid #ccc !important;

    thead {
      background-color: #f5f5f5;
    }

    th, td {
      border: none !important;
      padding: 0.7rem 1rem !important;
    }
  }

  &__col-exec-date {
    width: 20%;
  }

  &__col-exec-checksum {
    width: 20%;
  }

  &__col-exec-status {
    width: 20%;
  }

  &__col-exec-duration {
    width: 20%;
  }

  &__col-exec-results {
    width: 20%;
  }

  &__design-link, &__reports-link {
    cursor: pointer;
  }

  &__execution-design {
    width: 100%;
    height: 600px;
  }
}