<div data-bind="css: classes()">
  <loading data-bind="css: classes('loading-panel'), visible: loading" params="status: ko.i18n('common.loadingWithDots', 'Loading...')"></loading>
  <div data-bind="visible: !loading()">
    <h2 data-bind="css: classes('title'), text: ko.i18n('components.analysisExecution.title', 'Executions')"></h2>
    <label data-bind="css: classes('only-results-checkbox')"><input type="checkbox" data-bind="checked: showOnlySourcesWithResults"> Show only sources with results</label>
    <div data-bind="css: classes('content')">
      <table data-bind="
          css: classes('execution-groups-table'),
          dataTable: {
            data: filteredExecutionGroups,
            options: {
              columns: sourcesColumn,
              order: [[ 0, 'asc' ]],
              pageLength: tableOptions.pageLength,
              language: ko.i18n('datatable.language')
            }
          }
        "></table>
    </div>
  </div>
</div>

<atlas-modal params="
  showModal: $component.isExecutionDesignShown,
  title: ko.i18n('components.analysisExecution.designModal.title', 'Design'),
  data: {
    executionDesign: $component.executionDesign,
    classes: $component.classes
  }
">
  <loading data-bind="css: classes('loading-panel'), visible: executionDesign() == null" params="status: ko.i18n('common.loadingWithDots', 'Loading...')"></loading>
  <div data-bind="if: executionDesign() != null">
    <textarea data-bind="css: classes('execution-design'), text: JSON.stringify(executionDesign(), null, 2)"></textarea>
  </div>
</atlas-modal>

<modal-exit-message params="{
    showModal: $component.isExitMessageShown,
    title: ko.i18n('components.analysisExecution.exitModal.title', 'Execution Exit Message'),
    exitMessage: $component.exitMessage,
  }
">
</modal-exit-message>


<script type="text/html" id="execution-group">
  <div data-bind="css: classes('group', isExpanded ? 'expanded': null )">
    <div data-bind="css: classes('heading')">
      <label data-bind="css: classes('ds-title'), text: sourceName"></label>
      <ul data-bind="css: classes({ element: 'action-list' })">
        <li data-bind="css: classes({ element: 'action' }),
          tooltip: !$component.isGenerationPermitted(sourceKey) ? $component.getDisableReason(sourceKey) : null">
          <button data-bind="css: classes({ element: 'action-btn', extra: ['btn btn-primary btn-sm']}),
            attr: { 'disabled': !$component.isGenerationPermitted(sourceKey) },
            click: () => $component.generate(sourceKey),
            visible: $component.executionStatuses.COMPLETED === status()">
            <i data-bind="css: classes({ element: 'action-ico', extra: ['fa fa-play']})"></i>
            <span data-bind="css: classes('action-text'), text: ko.i18n('components.analysisExecution.buttons.generate', 'Generate')"></span>
          </button>
          <a role="button" class="btn btn-sm btn-danger" data-bind="click: () => $component.cancelGenerate(sourceKey),
            attr: { disabled: $component.isSourceStopping($data) },
            visible: $component.runningExecutionStatuses.includes(status()) && !$component.runExecutionInParallel">
            <i class="fa fa-spinner fa-spin"></i> <span data-bind="text: ko.i18n('common.cancel', 'Cancel')"></span>
          </a>
          <a role="button" class="btn btn-sm btn-primary" data-bind="click: () => $component.generate(sourceKey),
            visible: $component.runningExecutionStatuses.includes(status()) && $component.runExecutionInParallel">
            <i class="fa fa-spinner fa-spin"></i> <span data-bind="text: ko.i18n('components.analysisExecution.buttons.generating', 'Generating')"></span>
          </a>
          <a role="button" class="btn btn-sm btn-warning" data-bind="attr: { disabled: true },
            visible: $component.executionStatuses.PENDING === status()">
            <i class="fa fa-spinner fa-spin"></i> <span data-bind="text: ko.i18n('components.analysisExecution.buttons.pending', 'Pending')"></span>
          </a>
        </li>
        <li data-bind="css: classes({ element: 'action' }),
          tooltip: !$component.isResultsViewPermitted(sourceKey) ? ko.i18n('components.analysisExecution.messages.permissions', 'Not enough permissions to view results') : null">
          <!-- ko if: $component.executionResultMode === $component.executionResultModes.VIEW -->
            <button data-bind="
              css: classes({ element: 'action-btn', extra: ['btn btn-primary btn-sm']}),
              attr: { 'disabled': !$component.isResultsViewPermitted(sourceKey) },
              click: () => $component.goToLatestResults(sourceKey)">
              <i data-bind="css: classes({ element: 'action-ico', extra: ['fa fa-eye']})"></i>
              <span data-bind="text: ko.i18n('components.analysisExecution.buttons.latestResult', 'View latest result')"></span>
            </button>
          <!-- /ko -->
          <!-- ko if: $component.executionResultMode === $component.executionResultModes.DOWNLOAD -->
            <button data-bind="css: classes({ element: 'action-btn', extra: ['btn btn-primary btn-sm']}),
              attr: { 'disabled': !$component.isResultsViewPermitted(sourceKey) },
              click: () => $component.downloadLatestResults(sourceKey)">
              <i data-bind="css: classes({ element: 'action-ico', extra: ['fa fa-download']})"></i>
              <span data-bind="text: ko.i18n('components.analysisExecution.buttons.downloadLatestResult', 'Download latest result')"></span>
            </button>
          <!-- /ko -->
        </li>
        <li data-bind="css: classes({ element: 'action' }),
          tooltip: !$component.isResultsViewPermitted(sourceKey) ? ko.i18n('components.analysisExecution.messages.permissions', 'Not enough permissions to view results') : null">
          <button data-bind="css: classes({ element: 'action-btn', extra: ['btn btn-primary btn-sm']}),
            attr: { 'disabled': !$component.isResultsViewPermitted(sourceKey) },
            click: toggleSection">
            <i data-bind="css: classes({ element: 'action-ico', extra: ['fa fa-ellipsis-v']})"></i>
            <span data-bind="text: ko.i18nformat('components.analysisExecution.buttons.allExecutions', 'All executions (<%=submissions%>)', {submissions: submissions().length})"></span>
          </button>
        </li>
 			<!-- ko if: $component.config.shinyEnabled -->
      <div class="btn-group">
        <button class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown"
          data-bind="attr: {disabled: !$component.isResultsViewPermitted(sourceKey) || $component.runningExecutionStatuses.includes(status()) || submissions().length < 1}">
          <i class="fa fa-network-wired"></i>
          <span data-bind="text: 'Shiny App'"></span>
          <span class="caret"></span>
        </button>
        <ul class="dropdown-menu dropdown-menu-right" data-bind="foreach: $component.shinyOptions">
          <li>
            <a data-bind="click: () => $data.action($parent)" href="#">
              <div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
            </a>
          </li>
        </ul>
      </div>
			<!-- /ko -->
      </ul>
    </div>
    <ul data-bind="css: classes('result-list')">
      <li data-bind="css: classes('result-line')">
        <table data-bind="
          css: classes({ element: 'result-table', extra: ['table', 'table-bordered', 'table-hover'] }),
          dataTable: {
            data: submissions,
            options: {
              columns: execColumns,
              order: [[ 0, 'desc' ]],
              pageLength: $component.tableOptions.pageLength,
              language: ko.i18n('datatable.language')
            }
          }
        "></table>
      </li>
    </ul>
  </div>
</script>