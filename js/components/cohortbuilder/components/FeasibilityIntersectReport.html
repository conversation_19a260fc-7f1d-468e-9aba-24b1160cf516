<table style="border-collapse: collapse; width:100%">
	<tr>
		<td style="vertical-align: top; padding-right: 25px;">
			<div class="inclusionRuleType">
				<div class="all-any-selector">
					<span data-bind="text: ko.i18n('cohortDefinitions.cohortreports.having', 'Having')"></span>
					<select data-bind="options: allAnyOptions, optionsText: 'name', optionsValue: 'id', value: allAnyOption"></select>
					<span data-bind="text: ko.i18n('cohortDefinitions.cohortreports.ofSelectedCriteria', 'of selected criteria')"></span>
					<select data-bind="options: passedFailedOptions, optionsText: 'name', optionsValue: 'id', value: passedFailedOption"></select>
			    </div>

				<table>
					<thead>
						<th class="rule-checkbox" data-bind="click: headerCheckboxClicked"><i class="fa fa-check" data-bind="css: {selected: report().inclusionRuleStats.length === checkedRulesIds().length}"></i></th>
						<th style="width: 15px;"></th>
						<th class="header">
							<span data-bind="text: ko.i18n('components.feasibilityIntersectReport.inclusionRule', 'Inclusion Rule')"></span>
						</th>
						<th style="width: 100px;text-align: right" class="header">N</th>
						<th style="width: 100px;text-align: right" class="header">
							<span data-bind="text: ko.i18n('components.feasibilityIntersectReport.percentSatisfied', '% Satisfied')"></span>
						</th>
						<th style="width: 100px;text-align: right" class="header">
							<span data-bind="text: ko.i18n('components.feasibilityIntersectReport.percentToGain', '% To-Gain')"></span>
						</th>
					</thead>
					<tbody data-bind="foreach: report().inclusionRuleStats">
						<tr data-bind="css: {inclusionRulePass: ($parent.pass.indexOf($data) != -1), inclusionRuleFail: ($parent.fail.indexOf($data) != -1), inclusionRuleNotSelected: !$parent.isRuleChecked(id)}">
							<td class="rule-checkbox"><i class="fa fa-check" data-bind="css: {selected: $parent.isRuleChecked(id)}, click: () => $parent.checkRule(id)"></i></td>
							<td><span data-bind="text: ($index()+1) + '.'"></span>
							</td>
							<td style="text-align: left"><span data-bind="text:name"></span>
							</td>
							<td><span data-bind="text:countSatisfying.toLocaleString()"></span>
							</td>
							<td><span data-bind="text:percentSatisfying"></span>
							</td>
							<td><span data-bind="text:percentExcluded"></span>
							</td>
						</tr>
					</tbody>
				</table>

				<div class="rules-summary">
					<b data-bind="text: ko.i18n('cohortDefinitions.cohortreports.summary', 'Summary:')"></b>
					<span data-bind="text: ko.i18nformat('cohortDefinitions.cohortreports.summaryText',
							'<%=events%> events (<%=percentage%>%)',
							{events: summaryValue().toLocaleString(), percentage: summaryPercent().toFixed(2)})"></span>
				</div>
			</div>
		</td>
		<td style="padding-left: 25px; vertical-align: top; width: 400px; border-left: solid 1px #eee;">
			<div class="header">
				<span data-bind="text: ko.i18n('components.feasibilityIntersectReport.populationVisualization', 'Population Visualization')"></span>
			</div>
			<div class="visualization_container" data-bind="eventListener: [
																								 { event: 'mouseover', selector: '.cell rect', callback: handleCellOver},
																								 { event: 'mouseout', selector: '.cell rect', callback: describeClear}]">
				<div style="height: 25px; text-align: center; line-height: 25px; font-size: 12px; color: #222;" class="rect_summary" data-bind="text: rectSummary"></div>
				<div style="vertical-align: middle; text-align: center; width: 400px; margin: 7px auto 7px auto;" data-bind="populationTreemap: populationTreemapData, populationTreemapAfterRender:grayRectsInTreemap"></div>
			</div>
		</td>
	</tr>
</table>