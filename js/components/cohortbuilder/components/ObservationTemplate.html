<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%"/>
				<col />
			</colgroup>
			<tr>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_1', 'an observation of')"></span>
					<conceptset-selector 
					params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets, 
					defaultName: ko.i18n('components.conditionObservation.anyObservation', 'Any Observation')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> 
							<span data-bind="text: ko.i18n('components.conditionObservation.addAttribute', 'Add attribute...')"></span><span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
								<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px"/>
				<col />
			</colgroup>		
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_20', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_2', 'for the first time in the person\'s history')"></div>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_3', 'occurrence start is:')"></span>

					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: ObservationType() != null, visible: ObservationType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ObservationType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.observationType', 'Observation Type')"></span>

					<cycle-toggle-input params="{value: ObservationTypeExclude, options: $component.options.DomainTypeExcludeOptions}"></cycle-toggle-input>
					<concept-list params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: ObservationType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: ObservationTypeCS() != null, visible: ObservationTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ObservationTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.observationTypeCS', 'with observation type concept')"></span>
						<cycle-toggle-input params="{value: ObservationTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ObservationTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyObservationType', 'Any Observation Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ValueAsNumber() != null, visible: ValueAsNumber() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsNumber') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_4', 'with Value as Number')"></span>

					<numeric-range params="Range: ValueAsNumber"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: ValueAsString() != null, visible: ValueAsString() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsString') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_5', 'with Value as String')"></span>

					<text-filter-input params="Filter: ValueAsString"></text-filter-input>
				</td>
			</tr>
			<tr data-bind="if: ValueAsConcept() != null, visible: ValueAsConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_6', 'with Value as Concept:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Meas Value'}, ConceptList: ValueAsConcept()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ValueAsConceptCS() != null, visible: ValueAsConceptCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsConceptCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.valueAsConceptCS', 'with value as concept')"></span>
						<cycle-toggle-input params="{value: ValueAsConceptCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ValueAsConceptCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyValueAsConcept', 'Any Value As Concept')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Qualifier() != null, visible: Qualifier() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Qualifier') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_7', 'with Qualifier:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Condition'}, ConceptList: Qualifier()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: QualifierCS() != null, visible: QualifierCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('QualifierCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.qualifierCS', 'with qualifier concept')"></span>
						<cycle-toggle-input params="{value: QualifierCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: QualifierCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyQualifier', 'Any Qualifier')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Unit() != null, visible: Unit() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Unit') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_8', 'with Unit:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Unit'}, ConceptList: Unit()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: UnitCS() != null, visible: UnitCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('UnitCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.unitCS', 'with unit concept')"></span>
						<cycle-toggle-input params="{value: UnitCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: UnitCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyUnit', 'Any Unit')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ObservationSourceConcept() != null, visible: ObservationSourceConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ObservationSourceConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_9', 'Observation Source Concept is:')"></span>

						<conceptset-selector params="conceptSetId: ObservationSourceConcept(), conceptSets: $component.expression.ConceptSets, 
						defaultName: ko.i18n('components.conditionObservation.anyObservation', 'Any Observation')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_10', 'with age')"></span>

					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_11', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialty() != null, visible: ProviderSpecialty() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialty') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_12', 'with a Provider Specialty of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Provider', DefaultQuery: ''}, ConceptList: ProviderSpecialty()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialtyCS() != null, visible: ProviderSpecialtyCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialtyCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.providerSpecialtyCS', 'with provider specialty concept')"></span>
						<cycle-toggle-input params="{value: ProviderSpecialtyCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ProviderSpecialtyCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyProviderSpecialty', 'Any Provider Ppecialty')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: VisitType() != null, visible: VisitType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservation.conditionObservationText_13', 'with a Visit occurrence of:')"></span>

					<concept-list params="PickerParams: { DefaultDomain: 'Visit', DefaultQuery: ''}, ConceptList: VisitType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: VisitTypeCS() != null, visible: VisitTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.visitTypeCS', 'with visit type concept')"></span>
						<cycle-toggle-input params="{value: VisitTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: VisitTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyVisitType', 'Any Visit Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}"></criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>
