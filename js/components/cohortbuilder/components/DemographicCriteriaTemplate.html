<div class="criteriaSection" style="display: table; width: 100%;" data-bind="with: Criteria">
	<div style="display: table-cell">
		<table class="criteriaTable">
			<col style="width:16px" />
			<col />
			<tr>
				<td colspan="2">
					<div class="btn-group pull-left">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i>
							<span data-bind="text: ko.i18n('components.demographicCriteria.addAttribute', 'Add attribute...')"></span>
							<span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
								<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.demographicCriteria.withAge', 'with age')"></span>
					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.demographicCriteria.withGenderOf', 'with a gender of:')"></span>
						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Race() != null, visible: Race() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Race') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.demographicCriteria.withRaceOf', 'with a race of:')"></span>
						<concept-list params="PickerParams: { DefaultDomain: 'Race', DefaultQuery: ''}, ConceptList: Race()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: RaceCS() != null, visible: RaceCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RaceCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.raceCS', 'with race concept')"></span>
						<cycle-toggle-input params="{value: RaceCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: RaceCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyRace', 'Any Race')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Ethnicity() != null, visible: Ethnicity() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Ethnicity') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.demographicCriteria.withEthnicity', 'with a ethnicity of:')"></span>
						<concept-list params="PickerParams: { DefaultDomain: 'Ethnicity', DefaultQuery: ''}, ConceptList: Ethnicity()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: EthnicityCS() != null, visible: EthnicityCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RaceCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.ethnicityCS', 'with ethnicity concept')"></span>
						<cycle-toggle-input params="{value: EthnicityCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: EthnicityCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyEthnicity', 'Any Ethnicity')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td>
					<i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" />
				</td>
				<td>
					<span data-bind="text: ko.i18n('components.demographicCriteria.occurrenceStart', 'occurrence start is:')"></span>
					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceEndDate() != null, visible: OccurrenceEndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceEndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.demographicCriteria.occurrenceEnd', 'occurrence end is:')"></span>
					<date-range params="Range: OccurrenceEndDate"></date-range>
				</td>
			</tr>
		</table>
	</div>
</div>
