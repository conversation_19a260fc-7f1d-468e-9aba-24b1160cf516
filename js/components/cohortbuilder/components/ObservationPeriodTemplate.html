<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%"/>
				<col />
			</colgroup>
			<tr>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_1', 'observation periods with the following criteria:')"></span>

				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> 
							<span data-bind="text: ko.i18n('components.conditionObservationPeriod.addAttribute', 'Add attribute...')"></span><span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
								<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px"/>
				<col />
			</colgroup>		
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_13', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_2', 'limited to the patients first observation period')"></span>

					</div>
				</td>
			</tr>
			<tr data-bind="if: UserDefinedPeriod() != null, visible: UserDefinedPeriod() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('UserDefinedPeriod') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_3', 'using specified period:')"></span>

					<period-input params="Period: UserDefinedPeriod"></period-input>
				</td>
			</tr>
			<tr data-bind="if: PeriodStartDate() != null, visible: PeriodStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('PeriodStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_4', 'observation period start is:')"></span>

					<date-range params="Range: PeriodStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: PeriodEndDate() != null, visible: PeriodEndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('PeriodEndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_5', 'observation period end is:')"></span>

					<date-range params="Range: PeriodEndDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: PeriodType() != null, visible: PeriodType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('PeriodType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_6', 'Period Type is:')"></span>

					<concept-list params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: PeriodType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: PeriodTypeCS() != null, visible: PeriodTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('PeriodTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.periodTypeCS', 'with a period type concept')"></span>
						<cycle-toggle-input params="{value: PeriodTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: PeriodTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyPeriodType', 'Any Period Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: AgeAtStart() != null, visible: AgeAtStart() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtStart') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_7', 'with age in years at period start')"></span>

					<numeric-range params="Range: AgeAtStart"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: AgeAtEnd() != null, visible: AgeAtEnd() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtEnd') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_8', 'with age in years at period end')"></span>

					<numeric-range params="Range: AgeAtEnd"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: PeriodLength() != null, visible: PeriodLength() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('PeriodLength') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_9', 'with period duration')"></span>

					<numeric-range params="Range: PeriodLength"></numeric-range>
					<span data-bind="text: ko.i18n('components.conditionObservationPeriod.conditionObservationText_10', 'days.')"></span>

				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: 'The index date refers to the observation period.'}"></criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>
