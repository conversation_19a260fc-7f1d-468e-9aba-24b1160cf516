<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%" />
				<col />
			</colgroup>
			<tr>
				<td> <span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_1', 'a drug era of')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets,
					defaultName: ko.i18n('components.conditionDrug.anyDrug', 'Any Drug')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i
								class="fa fa-plus"></i>
							<span data-bind="text: ko.i18n('components.conditionDrug.addAttribute', 'Add attribute...')"></span><span
								class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
									<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
									<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)">
									</div>
								</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px" />
				<col />
			</colgroup>
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_13', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div
						data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_2', 'for the first time in the person\'s history')">
					</div>
				</td>
			</tr>
			<tr data-bind="if: EraStartDate() != null, visible: EraStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_3', 'era start is:')"></span>

					<date-range params="Range: EraStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: EraEndDate() != null, visible: EraEndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraEndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_4', 'era end is:')"></span>

					<date-range params="Range: EraEndDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceCount() != null, visible: OccurrenceCount() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceCount') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_5', 'with exposure count')"></span>

					<numeric-range params="Range: OccurrenceCount"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: GapDays() != null, visible: GapDays() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GapDays') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_6', 'with gap days of')"></span>
					<numeric-range params="Range: GapDays"></numeric-range>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_7', 'days')"></span>
				</td>
			</tr>
			<tr data-bind="if: EraLength() != null, visible: EraLength() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraLength') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_8', 'with era length')"></span>

					<numeric-range params="Range: EraLength"></numeric-range>
					<span data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_7', 'days')"></span>
				</td>
			</tr>
			<tr data-bind="if: AgeAtStart() != null, visible: AgeAtStart() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtStart') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_9', 'with age in years at era start')"></span>

					<numeric-range params="Range: AgeAtStart"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: AgeAtEnd() != null, visible: AgeAtEnd() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtEnd') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_10', 'with age in years at era end')"></span>

					<numeric-range params="Range: AgeAtEnd"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDrug.conditionDrugText_11', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()">
						</concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group
						params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}">
					</criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>