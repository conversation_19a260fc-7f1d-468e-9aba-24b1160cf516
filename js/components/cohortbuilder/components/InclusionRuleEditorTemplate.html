<!-- ko with: InclusionRule -->
<div class="inclusion-rule-header">
	<div class="input-group">
		<input type="text" class="form-control" maxlength="255" 
		data-bind="attr: {placeholder: ko.i18n('components.inclusionRuleEditor.unnamedCriteria', 'Unnamed Criteria')}, textInput: name, css: { emptyInput: !(name() && (name().length > 0)) }" />
		<div class="input-group-btn">
			<button class="btn btn-sm btn-primary copyInclusionRule">
				<span data-bind="text: ko.i18n('components.inclusionRuleEditor.copy', 'Copy')"></span>
			</button>
			<button class="btn btn-sm btn-danger deleteInclusionRule">
				<span data-bind="text: ko.i18n('components.inclusionRuleEditor.delete', 'Delete')"></span>
			</button>
		</div>
	</div>
</div>
<div class="divtext" data-bind="contentEditable: description, placeholder: ko.i18n('components.inclusionRuleEditor.ruleDescriptionPlaceholder', 'enter an inclusion rule description')"></div>
<!-- /ko -->
<criteria-group params="{expression: IndexRule, group: InclusionRule().expression, indexMessage: ko.i18n('components.cohortExpressionEditor.indexMessage', 'The index date refers to the event from the Cohort Entry criteria.')}"></criteria-group>
