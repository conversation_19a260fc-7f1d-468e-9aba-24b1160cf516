<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%"/>
				<col />
			</colgroup>
			<tr>
				<td>
					<span data-bind="text: ko.i18n('components.locationRegion.criteria.of', 'a location region of')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets, defaultName: ko.i18n('components.locationRegion.anyLocationRegion', 'Any Region')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> <span data-bind="text: ko.i18n('components.common.addAttribute', 'Add attribute...')"></span><span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: text"></div>
								<div class="optionDescription" data-bind="text: description"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px"/>
				<col />
			</colgroup>
			<tr data-bind="if: StartDate() != null, visible: StartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('StartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.locationRegion.criteria.startDate.text', 'start is:')"></span>
					<date-range params="Range: StartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: EndDate() != null, visible: EndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.locationRegion.criteria.endDate.text', 'end is:')"></span>
					<date-range params="Range: EndDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}"></criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>
