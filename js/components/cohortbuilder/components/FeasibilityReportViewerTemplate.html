
<!-- ko if: report -->
<table class="indexSummary">
	<thead>
		<th style="width: 15px;"></th>
		<th></th>
		<th>
			<span data-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_1', 'Match Rate')"></span>
		</th>
		<th>
			<span data-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_2', 'Matches')"></span>
		</th>
		<!-- ko if: report().summary.lostCount != 0 -->
		<th>
			<span data-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_3', 'Lost from censoring')"></span>
		</th>
		<!-- /ko -->
		<th>
			<span data-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_4', 'Total Events')"></span>
		</th>
	</thead>
	<tbody>
		<td></td>
		<td class="header">
			<span data-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_5', 'Summary Statistics:')"></span>
		</td>
		<td data-bind="text:report().summary.percentMatched"></td>
		<td data-bind="text:report().summary.finalCount.toLocaleString()"></td>
		<!-- ko if: report().summary.lostCount != 0 -->
		<td data-bind="text:report().summary.lostCount.toLocaleString()"></td>
		<!-- /ko -->
		<td data-bind="text:report().summary.baseCount.toLocaleString()"></td>
	</tbody>
</table>

<!-- ko if: report().inclusionRuleStats.length > 0 -->
<div style="position: relative">
<!-- ko if: selectedView() == 'intersect' -->
<span class="toggleView" style="position: absolute; right:5px" 
data-bind="click: function() { selectedView('attrition'); }, text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_6', 'Switch to attrition view')"></span> <!-- /ko -->
<!-- ko if: selectedView() == 'attrition' -->
<span class="toggleView" style="position: absolute; right:5px" 
data-bind="click: function() { selectedView('intersect'); }, text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_7', 'Switch to intersect view')"></span> <!-- /ko -->
</div>
<div data-bind="visible: selectedView() == 'intersect'">
	<feasibility-intersect-report params="reportType: reportType, report: report"></feasibility-intersect-report></div>
<div data-bind="visible: selectedView() == 'attrition'">
	<feasibility-attrition-report params="reportType: reportType, report: report"></feasibility-attrition-report></div>
<!-- /ko -->
<!-- ko if: report().inclusionRuleStats.length == 0 -->
<div class="no-inclusion-message__container">
	<div>
		<span dta-bind="text: ko.i18n('components.feasibilityReportViewer.feasibilityReportViewerText_8', 'No inclusion rules specified for this cohort definition.')"></span>
	</div>
</div>
<!-- /ko -->
<!-- /ko -->