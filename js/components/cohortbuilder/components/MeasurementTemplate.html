<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%"/>
				<col />
			</colgroup>
			<tr>
				<td> <span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_1', 'a measurement of')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets, defaultName: 
					ko.i18n('components.conditionMeasurement.anyMeasurement', 'Any Measurement')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> 
							<span data-bind="text: ko.i18n('components.conditionMeasurement.addAttribute', 'Add attribute...')"></span><span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
								<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px"/>
				<col />
			</colgroup>		
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_29', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_2', 'for the first time in the person\'s history')"></div>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_3', 'occurrence start is:')"></span>
					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: MeasurementType() != null, visible: MeasurementType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('MeasurementType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionMeasurement.measurementType', 'Measurement Type')"></span>
					<cycle-toggle-input params="{value:  MeasurementTypeExclude, options: $component.options.DomainTypeExcludeOptions}"></cycle-toggle-input>
					<concept-list params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: MeasurementType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: MeasurementTypeCS() != null, visible: MeasurementTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('MeasurementTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.measurementTypeCS', 'with measurement type concept')"></span>
						<cycle-toggle-input params="{value: MeasurementTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: MeasurementTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyMeasurementType', 'Any Measurement Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Operator() != null, visible: Operator() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Operator') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_5', 'Measurement Value Operator is:')"></span>
					<concept-list params="PickerParams: { DefaultDomain: 'Meas Value Operator', DefaultQuery: ''}, ConceptList: Operator()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: OperatorCS() != null, visible: OperatorCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OperatorCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.operatorCS', 'with an operator concept')"></span>
						<cycle-toggle-input params="{value: OperatorCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: OperatorCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyOperator', 'Any Operator')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ValueAsNumber() != null, visible: ValueAsNumber() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsNumber') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_6', 'with value as number')"></span>
					<numeric-range params="Range: ValueAsNumber"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: ValueAsConcept() != null, visible: ValueAsConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_7', 'Value as Concept is:')"></span>
					<concept-list params="PickerParams: { DefaultDomain: 'Meas Value', DefaultQuery: ''}, ConceptList: ValueAsConcept()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: ValueAsConceptCS() != null, visible: ValueAsConceptCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ValueAsConceptCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.valueAsConceptCS', 'with value as concept')"></span>
						<cycle-toggle-input params="{value: ValueAsConceptCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ValueAsConceptCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyValueAsConcept', 'Any Value As Concept')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Unit() != null, visible: Unit() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Unit') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_8', 'Unit is:')"></span>
					<concept-list params="PickerParams: { DefaultDomain: 'Unit', DefaultQuery: ''}, ConceptList: Unit()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: UnitCS() != null, visible: UnitCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('UnitCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.unitCS', 'with unit concept')"></span>
						<cycle-toggle-input params="{value: UnitCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: UnitCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyUnit', 'Any Unit')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: RangeLow() != null, visible: RangeLow() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RangeLow') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_9', 'with Range Low')"></span>
					<numeric-range params="Range: RangeLow"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: RangeLowRatio() != null, visible: RangeLowRatio() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RangeLowRatio') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_10', 'with Range Low Ratio')"></span>
					<numeric-range params="Range: RangeLowRatio"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: RangeHigh() != null, visible: RangeHigh() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RangeHigh') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_11', 'with Range High')"></span>
					<numeric-range params="Range: RangeHigh"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: RangeHighRatio() != null, visible: RangeHighRatio() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RangeHighRatio') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_12', 'with Range High Ratio')"></span>
					<numeric-range params="Range: RangeHighRatio"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Abnormal() != null, visible: Abnormal() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Abnormal') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_13', 'with an abnormal result')"></span>
				</td>
			</tr>
			<tr data-bind="if: MeasurementSourceConcept() != null, visible: MeasurementSourceConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('MeasurementSourceConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>

						<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_14', 'Measurement Source Concept is')"></span>
						<conceptset-selector params="conceptSetId: MeasurementSourceConcept(), conceptSets: $component.expression.ConceptSets, 
						defaultName: ko.i18n('components.conditionMeasurement.anyMeasurement', 'Any Measurement')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_15', 'with age')"></span>
					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>

						<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_16', 'with a gender of:')"></span>
						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialty() != null, visible: ProviderSpecialty() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialty') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>

						<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_17', 'with a Provider Specialty of:')"></span>
						<concept-list params="PickerParams: { DefaultDomain: 'Provider', DefaultQuery: ''}, ConceptList: ProviderSpecialty()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialtyCS() != null, visible: ProviderSpecialtyCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialtyCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.providerSpecialtyCS', 'with provider specialty concept')"></span>
						<cycle-toggle-input params="{value: ProviderSpecialtyCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ProviderSpecialtyCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyProviderSpecialty', 'Any Provider Ppecialty')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: VisitType() != null, visible: VisitType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>

					<span data-bind="text: ko.i18n('components.conditionMeasurement.conditionMeasurementText_18', 'with a Visit of:')"></span>
					<concept-list params="PickerParams: { DefaultDomain: 'Visit', DefaultQuery: ''}, ConceptList: VisitType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: VisitTypeCS() != null, visible: VisitTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.visitTypeCS', 'with visit type concept')"></span>
						<cycle-toggle-input params="{value: VisitTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: VisitTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyVisitType', 'Any Visit Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}"></criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>
