<div class="criteriaHeading">
    <div class="row">
        <div class="col-sm-4"
             data-bind="text: ko.i18n('components.cohortExpressionEditor.cohortEntryEventsText_2', 'Events having any of the following criteria:')">
        </div>
        <div class="col-sm-8">
            <div class="btn-group pull-right">
                <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown">
                    <i class="fa fa-plus"></i>
                    <span data-bind="text: buttonText"></span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" data-bind="foreach:$component.primaryCriteriaOptions">
                    <li>
                        <a data-bind="click:action" href="#">
                            <div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
                            <div class="optionDescription"
                                 data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<table class="criteria">
    <colgroup>
        <col span="1" class="rule" />
        <col span="1" class="delete" />
    </colgroup>
    <tbody
            data-bind="sortable: {data: ko.unwrap(ko.unwrap(expression).PrimaryCriteria).CriteriaList, connectClass : 'primaryCriteria', options: {cancel: ':input, button, [contenteditable], .undraggable'}}">
    <tr>
        <td>
            <div class="criteria-content">
                <div data-bind='component: {
											name: $component.getCriteriaIndexComponent($data),
											params: {expression: ko.unwrap($component.expression), criteria: $data}
										}'></div>
            </div>
        </td>
        <td style="vertical-align: top;">
            <button class="btn btn-sm btn-danger"
                    data-bind="text: ko.i18n('components.cohortExpressionEditor.deleteCriteria', 'Delete Criteria'), click: function (data, event) { $component.removePrimaryCriteria(data) }"></button>
        </td>
    </tr>
    </tbody>
</table>

<from-reusables-modal params="{isModalShown: $component.showReusablesModal, type: 'INITIAL_EVENT', parentExpression: $component.expression, callback: $component.insertFromReusable}"></from-reusables-modal>
