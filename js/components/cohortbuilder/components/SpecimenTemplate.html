<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%"/>
				<col />
			</colgroup>
			<tr>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_1', 'a specimen of')"></span>

					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets, defaultName: 
					ko.i18n('components.conditionSpecimen.anySpecimen', 'Any Specimen')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> 
							<span data-bind="text: ko.i18n('components.conditionSpecimen.addAttribute', 'Add attribute...')"></span><span class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
								<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
								<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)"></div>
							</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px"/>
				<col />
			</colgroup>		
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_15', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_2', 'for the first time in the person\'s history')"></div>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_3', 'occurrence start is:')"></span>

					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: SpecimenType() != null, visible: SpecimenType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('SpecimenType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.specimenType', 'Specimen Type')"></span>

					<cycle-toggle-input params="{value: SpecimenTypeExclude, options: $component.options.DomainTypeExcludeOptions}"></cycle-toggle-input>
					<concept-list params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: SpecimenType()"></concept-list>
				</td>
			</tr>
			<tr data-bind="if: SpecimenTypeCS() != null, visible: SpecimenTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('SpecimenTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.specimenTypeCS', 'with specimen type concept')"></span>
						<cycle-toggle-input params="{value: SpecimenTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: SpecimenTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anySpecimenType', 'Any Specimen Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Quantity() != null, visible: Quantity() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Quantity') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times"  /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_4', 'with Quantity')"></span>

					<numeric-range params="Range: Quantity"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Unit() != null, visible: Unit() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Unit') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_5', 'with Unit:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Unit'}, ConceptList: Unit()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: UnitCS() != null, visible: UnitCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('UnitCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.unitCS', 'with unit concept')"></span>
						<cycle-toggle-input params="{value: UnitCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: UnitCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyUnit', 'Any Unit')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: AnatomicSite() != null, visible: AnatomicSite() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AnatomicSite') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_6', 'with Anatomic Site:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Spec Anatomic Site'}, ConceptList: AnatomicSite()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: AnatomicSiteCS() != null, visible: AnatomicSiteCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AnatomicSiteCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.atomicSiteCS', 'with atomic site concept')"></span>
						<cycle-toggle-input params="{value: AnatomicSiteCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: AnatomicSiteCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyAnatomicSite', 'Any Anatomic Site')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: DiseaseStatus() != null, visible: DiseaseStatus() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DiseaseStatus') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_7', 'with Disease Status:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Spec Disease Status'}, ConceptList: DiseaseStatus()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: DiseaseStatusCS() != null, visible: DiseaseStatusCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DiseaseStatusCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.diseaseStatusCS', 'with atomic site concept')"></span>
						<cycle-toggle-input params="{value: DiseaseStatusCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: DiseaseStatusCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyDiseaseStatus', 'Any Disease Status')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: SourceId() != null, visible: SourceId() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('SourceId') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_8', 'with Source ID')"></span>

					<text-filter-input params="Filter: SourceId"></text-filter-input>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_9', 'with age')"></span>

					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionSpecimen.conditionSpecimeText_10', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}"></criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>
