<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%" />
				<col />
			</colgroup>
			<tr>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDeath.conditionDeathText_1', 'a death occurrence from')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets,
						defaultName: ko.i18n('components.conditionDeath.anyDeath', 'Any Death')">
					</conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i
								class="fa fa-plus"></i>
							<span data-bind="text: ko.i18n('components.conditionDeath.addAttribute', 'Add attribute...')"></span><span
								class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
									<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
									<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)">
									</div>
								</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px" />
				<col />
			</colgroup>
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDeath.attributeText_7', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDeath.conditionDeathText_2', 'occurrence start is:')"></span>
					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: DeathType() != null, visible: DeathType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DeathType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDeath.deathType', 'Death Type')"></span>

					<cycle-toggle-input params="{value:  DeathTypeExclude, options: $component.options.DomainTypeExcludeOptions}">
					</cycle-toggle-input>
					<concept-list
						params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: DeathType()">
					</concept-list>
				</td>
			</tr>
			<tr data-bind="if: DeathTypeCS() != null, visible: DeathTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DeathTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.deathTypeCS', 'with death type concept')"></span>
						<cycle-toggle-input params="{value: DeathTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: DeathTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyDeathType', 'Any Death Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: DeathSourceConcept() != null, visible: DeathSourceConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DeathSourceConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDeath.conditionDeathText_3', 'Death Source Concept is')"></span>

						<conceptset-selector
							params="conceptSetId: DeathSourceConcept(), conceptSets: $component.expression.ConceptSets,
								defaultName: ko.i18n('components.conditionDeath.anyDeath', 'Any Death')">
						</conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDeath.conditionDeathText_4', 'with age')"></span>
					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDeath.conditionDeathText_5', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()">
						</concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group
						params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}">
					</criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>