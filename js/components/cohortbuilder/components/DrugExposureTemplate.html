<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%" />
				<col />
			</colgroup>
			<tr>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_1', 'a drug exposure of')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets,
						defaultName: ko.i18n('components.conditionDrugExposure.anyDrug', 'Any Drug')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i
								class="fa fa-plus"></i>
							<span
								data-bind="text: ko.i18n('components.conditionDrugExposure.addAttribute', 'Add attribute...')"></span><span
								class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
									<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
									<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)">
									</div>
								</a>
							</li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px" />
				<col />
			</colgroup>
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_23', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_2', 'for the first time in the person\'s history')">
					</div>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceStartDate() != null, visible: OccurrenceStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_3', 'occurrence start is:')"></span>

					<date-range params="Range: OccurrenceStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: OccurrenceEndDate() != null, visible: OccurrenceEndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('OccurrenceEndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_4', 'occurrence end is:')"></span>

					<date-range params="Range: OccurrenceEndDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: DrugType() != null, visible: DrugType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DrugType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDrugExposure.drugType', 'Drug Type')"></span>

					<cycle-toggle-input params="{value:  DrugTypeExclude, options: $component.options.DomainTypeExcludeOptions}">
					</cycle-toggle-input>
					<concept-list
						params="PickerParams: { DefaultDomain: 'Type Concept', DefaultQuery: ''}, ConceptList: DrugType()">
					</concept-list>
				</td>
			</tr>
			<tr data-bind="if: DrugTypeCS() != null, visible: DrugTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DrugTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.drugTypeCS', 'with drug type concept')"></span>
						<cycle-toggle-input params="{value: DrugTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: DrugTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyDrugType', 'Any Drug Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: StopReason() != null, visible: StopReason() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('StopReason') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_5', 'with a Stop Reason')"></span>

					<text-filter-input params="Filter: StopReason"></text-filter-input>
				</td>
			</tr>
			<tr data-bind="if: Refills() != null, visible: Refills() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Refills') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_6', 'with Refills')"></span>

					<numeric-range params="Range: Refills"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Quantity() != null, visible: Quantity() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Quantity') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_7', 'with Quantity')"></span>

					<numeric-range params="Range: Quantity"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: DaysSupply() != null, visible: DaysSupply() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DaysSupply') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_8', 'with Days Supply')"></span>

					<numeric-range params="Range: DaysSupply"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: RouteConcept() != null, visible: RouteConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RouteConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_9', 'Route Concept is:')"></span>

					<concept-list params="PickerParams: { DefaultDomain: 'Route', DefaultQuery: ''}, ConceptList: RouteConcept()">
					</concept-list>
				</td>
			</tr>
			<tr data-bind="if: RouteConceptCS() != null, visible: RouteConceptCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RouteConceptCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.routeConceptCS', 'with route concept')"></span>
						<cycle-toggle-input params="{value: RouteConceptCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: RouteConceptCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyRouteConcept', 'Any Route')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: EffectiveDrugDose() != null, visible: EffectiveDrugDose() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('RouteConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_10', 'with Effective Drug Dose')"></span>

					<numeric-range params="Range: EffectiveDrugDose"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: DoseUnit() != null, visible: DoseUnit() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DoseUnit') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_11', 'Dose Unit is:')"></span>

					<concept-list params="PickerParams: { DefaultDomain: 'Unit', DefaultQuery: ''}, ConceptList: DoseUnit()">
					</concept-list>
				</td>
			</tr>
			<tr data-bind="if: DoseUnitCS() != null, visible: DoseUnitCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DoseUnitCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.doseUnitCS', 'with dose unit concept')"></span>
						<cycle-toggle-input params="{value: DoseUnitCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: DoseUnitCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyUnit', 'Any Unit')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: LotNumber() != null, visible: LotNumber() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('LotNumber') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_12', 'with a Lot Number')"></span>

					<text-filter-input params="Filter: LotNumber"></text-filter-input>
				</td>
			</tr>
			<tr data-bind="if: DrugSourceConcept() != null, visible: DrugSourceConcept() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DrugSourceConcept') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_13', 'Drug Source Concept is:')"></span>

						<conceptset-selector
							params="conceptSetId: DrugSourceConcept(), conceptSets: $component.expression.ConceptSets, defaultName: ko.i18n('components.conditionDrugExposure.anyDrug', 'Any Drug')">
						</conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: Age() != null, visible: Age() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Age') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_14', 'with age')"></span>

					<numeric-range params="Range: Age"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_15', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()">
						</concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
						<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialty() != null, visible: ProviderSpecialty() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialty') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_16', 'with a Provider Specialty of:')"></span>

						<concept-list
							params="PickerParams: { DefaultDomain: 'Provider', DefaultQuery: ''}, ConceptList: ProviderSpecialty()">
						</concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: ProviderSpecialtyCS() != null, visible: ProviderSpecialtyCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('ProviderSpecialtyCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.providerSpecialtyCS', 'with provider specialty concept')"></span>
						<cycle-toggle-input params="{value: ProviderSpecialtyCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: ProviderSpecialtyCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyProviderSpecialty', 'Any Provider Ppecialty')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: VisitType() != null, visible: VisitType() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitType') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDrugExposure.conditionDrugExposureText_17', 'with a Visit occurrence of:')"></span>

					<concept-list params="PickerParams: { DefaultDomain: 'Visit', DefaultQuery: ''}, ConceptList: VisitType()">
					</concept-list>
				</td>
			</tr>
			<tr data-bind="if: VisitTypeCS() != null, visible: VisitTypeCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('VisitTypeCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.visitTypeCS', 'with visit type concept')"></span>
						<cycle-toggle-input params="{value: VisitTypeCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: VisitTypeCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyVisitType', 'Any Visit Type')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group
						params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}">
					</criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>