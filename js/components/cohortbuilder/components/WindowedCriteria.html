<div>
    <div data-bind="component: {
            name: $component.getCriteriaComponent($component.criteria().Criteria),
            params: {expression: $component.expression, criteria: $component.criteria().Criteria }
        }">
    </div>
    <div data-bind="css: classes('indexWindowSection')">

        <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_1', 'where')"></span>
        <cycle-toggle-input params="{value: criteria().StartWindow.UseEventEnd, options: $component.options.EventDateOptions}"></cycle-toggle-input> 
        <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_2', 'between')"></span>
        <window-input params="Window: criteria().StartWindow"></window-input> 
        <cycle-toggle-input params="{value: criteria().StartWindow.UseIndexEnd, options: $component.options.IndexDateOptions}"></cycle-toggle-input>
        <!-- ko if: criteria().EndWindow -->
        <div>
            <i data-bind="click: $component.removeEndWindow, css: classes({element: 'removeEndWindow', extra: 'fa fa-times'})"></i> 
            <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_3', 'and')"></span>
            <cycle-toggle-input params="{value: criteria().EndWindow().UseEventEnd, options: $component.options.EventDateOptions}"></cycle-toggle-input> 
            <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_2', 'between')"></span>
            <window-input params="Window: criteria().EndWindow()"></window-input> 
            <cycle-toggle-input params="{value: criteria().EndWindow().UseIndexEnd, options: $component.options.IndexDateOptions}"></cycle-toggle-input>
        </div>
        <!-- /ko -->
        <!-- ko ifnot: criteria().EndWindow -->
        <span class="linkish" data-bind="click: $component.addEndWindow"><i>
            <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_4', 'add additional constraint')"></span>
        </i></span>
        <!-- /ko -->
    </div>
    <div data-bind="css: classes('restrictVisitSection')">
        <input type="checkbox" data-bind="checked: criteria().RestrictVisit">
        <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_5', 'restrict to the same visit occurrence')"></span>

    </div>
    <div data-bind="css: classes('IgnoreObservationPeriodSection')">
        <input type="checkbox" data-bind="checked: criteria().IgnoreObservationPeriod, disable: disableObservationPeriod"> 
        <span data-bind="text:ko.i18n('components.windowedCriteria.windowedCriteriaText_6', 'ignore observation period')"></span>
    </div>
</div>