<div class="criteriaSection" data-bind="with: Criteria">
	<div style="position: relative">
		<table class="criteriaTable">
			<colgroup>
				<col style="width:100%" />
				<col />
			</colgroup>
			<tr>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_1', 'a dose era of')"></span>
					<conceptset-selector params="conceptSetId: CodesetId, conceptSets: $component.expression.ConceptSets,
					defaultName: ko.i18n('components.conditionDose.anyDrug', 'Any Drug')"></conceptset-selector>
				</td>
				<td>
					<div class="btn-group pull-right">
						<button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown"><i
								class="fa fa-plus"></i>
							<span data-bind="text: ko.i18n('components.conditionDose.addAttribute', 'Add attribute...')"></span><span
								class="caret"></span></button>
						<ul class="dropdown-menu" data-bind="foreach:$component.addActions">
							<li><a data-bind="click:action" href="#">
									<div class="optionText" data-bind="text: ko.i18n($data.title, $data.defaultTitle)"></div>
									<div class="optionDescription" data-bind="text: ko.i18n($data.description, $data.defaultDescription)">
									</div>
								</a></li>
						</ul>
					</div>
				</td>
			</tr>
		</table>
		<table class="criteriaTable">
			<colgroup>
				<col style="width:20px" />
				<col />
			</colgroup>
			<tr data-bind="if: DateAdjustment() != null, visible: DateAdjustment() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('DateAdjustment') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.attributeText_13', 'with date adjustment:')"></span>
					<date-adjustment params="DateAdjustment: DateAdjustment"></date-adjustment>
				</td>
			</tr>
			<tr data-bind="if: First() != null, visible: First() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('First') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div
						data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_2', 'for the first time in the person\'s history')">
					</div>
				</td>
			</tr>
			<tr data-bind="if: EraStartDate() != null, visible: EraStartDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraStartDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_3', 'era start is:')"></span>

					<date-range params="Range: EraStartDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: EraEndDate() != null, visible: EraEndDate() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraEndDate') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_4', 'era end is:')"></span>

					<date-range params="Range: EraEndDate"></date-range>
				</td>
			</tr>
			<tr data-bind="if: Unit() != null, visible: Unit() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Unit') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_5', 'with Unit:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Unit'}, ConceptList: Unit()"></concept-list>
					</div>
				</td>
			</tr>
			<tr data-bind="if: UnitCS() != null, visible: UnitCS() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('UnitCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span data-bind="text: ko.i18n('components.cohortCriteria.unitCS', 'with unit concept')"></span>
						<cycle-toggle-input params="{value: UnitCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
						</cycle-toggle-input>
						<conceptset-selector params="conceptSetId: UnitCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
							defaultName: ko.i18n('components.cohortCriteria.anyUnit', 'Any Unit')"></conceptset-selector>
					</div>
				</td>
			</tr>
			<tr data-bind="if: DoseValue() != null, visible: DoseValue() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('GapDays') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_6', 'with dose value')"></span>

					<numeric-range params="Range: DoseValue"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: EraLength() != null, visible: EraLength() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('EraLength') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_7', 'with era length')"></span>

					<numeric-range params="Range: EraLength"></numeric-range> days
				</td>
			</tr>
			<tr data-bind="if: AgeAtStart() != null, visible: AgeAtStart() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtStart') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_8', 'with age in years at era start')"></span>

					<numeric-range params="Range: AgeAtStart"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: AgeAtEnd() != null, visible: AgeAtEnd() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('AgeAtEnd') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<span
						data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_9', 'with age in years at era end')"></span>

					<numeric-range params="Range: AgeAtEnd"></numeric-range>
				</td>
			</tr>
			<tr data-bind="if: Gender() != null, visible: Gender() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('Gender') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td>
					<div>
						<span
							data-bind="text: ko.i18n('components.conditionDose.conditionDoseText_10', 'with a gender of:')"></span>

						<concept-list params="PickerParams: { DefaultDomain: 'Gender', DefaultQuery: ''}, ConceptList: Gender()">
						</concept-list>
					</div>
				</td>
			</tr>
		</tr>
		<tr data-bind="if: GenderCS() != null, visible: GenderCS() != null">
			<td><i data-bind="click: function() { $component.removeCriterion('GenderCS') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
			<td>
				<div>
					<span data-bind="text: ko.i18n('components.cohortCriteria.genderCS', 'with gender conept')"></span>
					<cycle-toggle-input params="{value: GenderCS().IsExclusion, options: $component.options.DomainTypeExcludeOptions}">
					</cycle-toggle-input>
					<conceptset-selector params="conceptSetId: GenderCS().CodesetId, conceptSets: $component.expression.ConceptSets, 
						defaultName: ko.i18n('components.cohortCriteria.anyGender', 'Any Gender')"></conceptset-selector>
				</div>
			</td>
		</tr>
		<tr data-bind="if: CorrelatedCriteria() != null, visible: CorrelatedCriteria() != null">
				<td><i data-bind="click: function() { $component.removeCriterion('CorrelatedCriteria') }, title: ko.i18n('common.remove', 'Remove')" class="fa fa-times" /></td>
				<td style="background-color: white">
					<criteria-group
						params="{expression: $component.expression, group: CorrelatedCriteria, indexMessage: $component.indexMessage}">
					</criteria-group>
				</td>
			</tr>
		</table>
	</div>
</div>