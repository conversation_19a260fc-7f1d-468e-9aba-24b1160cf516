<span contenteditable="true" class="dropdown numericInputField" data-bind="
        placeholder: ko.i18n('components.windowInput.windowInputText_1', 'All'),
        htmlValue: Window.Start.Days.numeric(),
        eventType: 'blur',
        ko_autocomplete: { value: Window.Start.Days, source: $component.options.windowDayOptions, minLength: 0, maxShowItems: 10, scroll: true },
        selectOnFocus: {events: ['click']},
    " />
<span data-bind="text:ko.i18n('components.windowInput.windowInputText_2', 'days')"></span>
<select data-bind="options: $component.options.windowCoeffOptions, optionsText: 'name', optionsValue: 'value', value: Window.Start.Coeff" />
<span data-bind="text:ko.i18n('components.windowInput.windowInputText_3', 'and')"></span>
<span contenteditable="true" class="dropdown numericInputField" data-bind="
        placeholder: ko.i18n('components.windowInput.windowInputText_1', 'All'),
        htmlValue: Window.End.Days.numeric(),
        eventType: 'blur', ko_autocomplete: { value: Window.End.Days, source: $component.options.windowDayOptions, minLength: 0, maxShowItems: 10, scroll: true },
        selectOnFocus: {events: ['click']},
" />
<span data-bind="text:ko.i18n('components.windowInput.windowInputText_2', 'days')"></span>
<select data-bind="options: $component.options.windowCoeffOptions, optionsText: 'name', optionsValue: 'value', value: Window.End.Coeff" />